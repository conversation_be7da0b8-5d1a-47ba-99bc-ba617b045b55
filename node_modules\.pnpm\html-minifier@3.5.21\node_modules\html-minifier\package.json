{"name": "html-minifier", "description": "Highly configurable, well-tested, JavaScript-based HTML minifier.", "version": "3.5.21", "keywords": ["cli", "compress", "compressor", "css", "html", "htmlmin", "javascript", "min", "minification", "minifier", "minify", "optimize", "optimizer", "pack", "packer", "parse", "parser", "uglifier", "uglify"], "homepage": "https://kangax.github.io/html-minifier/", "author": "<PERSON><PERSON><PERSON> \"kangax\" <PERSON>", "maintainers": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>> (http://perfectionkills.com/)"], "contributors": ["<PERSON> (https://github.com/gilmoreorless)", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>"], "license": "MIT", "bin": {"html-minifier": "./cli.js"}, "main": "src/htmlminifier.js", "repository": {"type": "git", "url": "git+https://github.com/kangax/html-minifier.git"}, "bugs": {"url": "https://github.com/kangax/html-minifier/issues"}, "engines": {"node": ">=4"}, "scripts": {"dist": "grunt dist", "test": "grunt test"}, "dependencies": {"camel-case": "3.0.x", "clean-css": "4.2.x", "commander": "2.17.x", "he": "1.2.x", "param-case": "2.1.x", "relateurl": "0.2.x", "uglify-js": "3.4.x"}, "devDependencies": {"grunt": "1.0.x", "grunt-browserify": "5.3.x", "grunt-contrib-uglify": "3.4.x", "gruntify-eslint": "4.0.x", "phantomjs-prebuilt": "2.1.x", "qunit": "2.x"}, "benchmarkDependencies": {"brotli": "1.3.x", "chalk": "2.4.x", "cli-table": "0.3.x", "lzma": "2.3.x", "minimize": "2.2.x", "progress": "2.0.x"}, "files": ["src/*.js", "cli.js", "sample-cli-config-file.conf"]}