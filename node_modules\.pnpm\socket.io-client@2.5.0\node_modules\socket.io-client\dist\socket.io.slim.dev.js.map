{"version": 3, "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///webpack/bootstrap 46c68a0009a152ba3192", "webpack:///./lib/index.js", "webpack:///./lib/url.js", "webpack:///./~/parseuri/index.js", "webpack:///./support/noop.js", "webpack:///./~/socket.io-parser/index.js", "webpack:///./~/component-emitter/index.js", "webpack:///./~/socket.io-parser/binary.js", "webpack:///./~/isarray/index.js", "webpack:///./~/socket.io-parser/is-buffer.js", "webpack:///./lib/manager.js", "webpack:///./~/engine.io-client/lib/index.js", "webpack:///./~/engine.io-client/lib/socket.js", "webpack:///./~/engine.io-client/lib/transports/index.js", "webpack:///./~/engine.io-client/lib/xmlhttprequest.js", "webpack:///./~/has-cors/index.js", "webpack:///./~/engine.io-client/lib/globalThis.browser.js", "webpack:///./~/engine.io-client/lib/transports/polling-xhr.js", "webpack:///./~/engine.io-client/lib/transports/polling.js", "webpack:///./~/engine.io-client/lib/transport.js", "webpack:///./~/engine.io-parser/lib/browser.js", "webpack:///./~/engine.io-parser/lib/keys.js", "webpack:///./~/has-binary2/index.js", "webpack:///./~/arraybuffer.slice/index.js", "webpack:///./~/after/index.js", "webpack:///./~/engine.io-parser/lib/utf8.js", "webpack:///./~/engine.io-parser/~/base64-arraybuffer/lib/base64-arraybuffer.js", "webpack:///./~/blob/index.js", "webpack:///./~/parseqs/index.js", "webpack:///./~/component-inherit/index.js", "webpack:///./~/yeast/index.js", "webpack:///./~/engine.io-client/lib/transports/polling-jsonp.js", "webpack:///./~/engine.io-client/lib/transports/websocket.js", "webpack:///ws (ignored)", "webpack:///./~/indexof/index.js", "webpack:///./lib/socket.js", "webpack:///./~/to-array/index.js", "webpack:///./lib/on.js", "webpack:///./~/component-bind/index.js", "webpack:///./~/backo2/index.js"], "names": ["url", "require", "parser", "Manager", "debug", "module", "exports", "lookup", "cache", "managers", "uri", "opts", "undefined", "parsed", "source", "id", "path", "sameNamespace", "nsps", "newConnection", "forceNew", "multiplex", "io", "query", "socket", "protocol", "connect", "Socket", "parseuri", "loc", "obj", "location", "host", "char<PERSON>t", "test", "port", "ipv6", "indexOf", "href", "eio", "Emitter", "on", "bind", "Backoff", "has", "Object", "prototype", "hasOwnProperty", "subs", "reconnection", "reconnectionAttempts", "Infinity", "reconnectionDelay", "reconnectionDelayMax", "randomizationFactor", "backoff", "min", "max", "jitter", "timeout", "readyState", "connecting", "lastPing", "encoding", "packetBuffer", "_parser", "encoder", "Encoder", "decoder", "Decoder", "autoConnect", "open", "emitAll", "emit", "apply", "arguments", "nsp", "call", "updateSocketIds", "generateId", "engine", "v", "length", "_reconnection", "_reconnectionAttempts", "_reconnectionDelay", "setMin", "_randomizationFactor", "setJitter", "_reconnectionDelayMax", "setMax", "_timeout", "maybeReconnectOnOpen", "reconnecting", "attempts", "reconnect", "fn", "self", "skipReconnect", "openSub", "onopen", "errorSub", "data", "cleanup", "err", "Error", "destroy", "timer", "setTimeout", "close", "push", "clearTimeout", "onping", "Date", "onpong", "ondata", "add", "ondecoded", "packet", "onerror", "onConnecting", "index", "splice", "type", "encode", "encodedPackets", "i", "write", "options", "processPacketQueue", "pack", "shift", "subsLength", "sub", "disconnect", "reset", "onclose", "reason", "delay", "duration", "onreconnect", "attempt", "toArray", "parseqs", "hasBin", "events", "connect_error", "connect_timeout", "error", "reconnect_attempt", "reconnect_failed", "reconnect_error", "ping", "pong", "json", "ids", "acks", "<PERSON><PERSON><PERSON><PERSON>", "send<PERSON><PERSON><PERSON>", "connected", "disconnected", "flags", "subEvents", "send", "args", "unshift", "ev", "binary", "BINARY_EVENT", "EVENT", "compress", "pop", "CONNECT", "onpacket", "rootNamespaceError", "ERROR", "onconnect", "onevent", "ACK", "onack", "BINARY_ACK", "DISCONNECT", "ondisconnect", "ack", "sent", "emitBuffered", "removeListener"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;ACVA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,uBAAe;AACf;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;;;;;;;;;;ACrCA;;;;AAIA,KAAIA,MAAM,mBAAAC,CAAQ,CAAR,CAAV;AACA,KAAIC,SAAS,mBAAAD,CAAQ,CAAR,CAAb;AACA,KAAIE,UAAU,mBAAAF,CAAQ,CAAR,CAAd;AACA,KAAIG,QAAQ,mBAAAH,CAAQ,CAAR,EAAiB,kBAAjB,CAAZ;;AAEA;;;;AAIAI,QAAOC,OAAP,GAAiBA,UAAUC,MAA3B;;AAEA;;;;AAIA,KAAIC,QAAQF,QAAQG,QAAR,GAAmB,EAA/B;;AAEA;;;;;;;;;;;;;AAaA,UAASF,MAAT,CAAiBG,GAAjB,EAAsBC,IAAtB,EAA4B;AAC1B,OAAI,QAAOD,GAAP,yCAAOA,GAAP,OAAe,QAAnB,EAA6B;AAC3BC,YAAOD,GAAP;AACAA,WAAME,SAAN;AACD;;AAEDD,UAAOA,QAAQ,EAAf;;AAEA,OAAIE,SAASb,IAAIU,GAAJ,CAAb;AACA,OAAII,SAASD,OAAOC,MAApB;AACA,OAAIC,KAAKF,OAAOE,EAAhB;AACA,OAAIC,OAAOH,OAAOG,IAAlB;AACA,OAAIC,gBAAgBT,MAAMO,EAAN,KAAaC,QAAQR,MAAMO,EAAN,EAAUG,IAAnD;AACA,OAAIC,gBAAgBR,KAAKS,QAAL,IAAiBT,KAAK,sBAAL,CAAjB,IACA,UAAUA,KAAKU,SADf,IAC4BJ,aADhD;;AAGA,OAAIK,EAAJ;;AAEA,OAAIH,aAAJ,EAAmB;;AAEjBG,UAAKnB,QAAQW,MAAR,EAAgBH,IAAhB,CAAL;AACD,IAHD,MAGO;AACL,SAAI,CAACH,MAAMO,EAAN,CAAL,EAAgB;;AAEdP,aAAMO,EAAN,IAAYZ,QAAQW,MAAR,EAAgBH,IAAhB,CAAZ;AACD;AACDW,UAAKd,MAAMO,EAAN,CAAL;AACD;AACD,OAAIF,OAAOU,KAAP,IAAgB,CAACZ,KAAKY,KAA1B,EAAiC;AAC/BZ,UAAKY,KAAL,GAAaV,OAAOU,KAApB;AACD;AACD,UAAOD,GAAGE,MAAH,CAAUX,OAAOG,IAAjB,EAAuBL,IAAvB,CAAP;AACD;;AAED;;;;;;AAMAL,SAAQmB,QAAR,GAAmBvB,OAAOuB,QAA1B;;AAEA;;;;;;;AAOAnB,SAAQoB,OAAR,GAAkBnB,MAAlB;;AAEA;;;;;;AAMAD,SAAQH,OAAR,GAAkB,mBAAAF,CAAQ,CAAR,CAAlB;AACAK,SAAQqB,MAAR,GAAiB,mBAAA1B,CAAQ,EAAR,CAAjB,C;;;;;;;;AC5FA;;;;AAIA,KAAI2B,WAAW,mBAAA3B,CAAQ,CAAR,CAAf;AACA,KAAIG,QAAQ,mBAAAH,CAAQ,CAAR,EAAiB,sBAAjB,CAAZ;;AAEA;;;;AAIAI,QAAOC,OAAP,GAAiBN,GAAjB;;AAEA;;;;;;;;;AASA,UAASA,GAAT,CAAcU,GAAd,EAAmBmB,GAAnB,EAAwB;AACtB,OAAIC,MAAMpB,GAAV;;AAEA;AACAmB,SAAMA,OAAQ,OAAOE,QAAP,KAAoB,WAApB,IAAmCA,QAAjD;AACA,OAAI,QAAQrB,GAAZ,EAAiBA,MAAMmB,IAAIJ,QAAJ,GAAe,IAAf,GAAsBI,IAAIG,IAAhC;;AAEjB;AACA,OAAI,aAAa,OAAOtB,GAAxB,EAA6B;AAC3B,SAAI,QAAQA,IAAIuB,MAAJ,CAAW,CAAX,CAAZ,EAA2B;AACzB,WAAI,QAAQvB,IAAIuB,MAAJ,CAAW,CAAX,CAAZ,EAA2B;AACzBvB,eAAMmB,IAAIJ,QAAJ,GAAef,GAArB;AACD,QAFD,MAEO;AACLA,eAAMmB,IAAIG,IAAJ,GAAWtB,GAAjB;AACD;AACF;;AAED,SAAI,CAAC,sBAAsBwB,IAAtB,CAA2BxB,GAA3B,CAAL,EAAsC;;AAEpC,WAAI,gBAAgB,OAAOmB,GAA3B,EAAgC;AAC9BnB,eAAMmB,IAAIJ,QAAJ,GAAe,IAAf,GAAsBf,GAA5B;AACD,QAFD,MAEO;AACLA,eAAM,aAAaA,GAAnB;AACD;AACF;;AAED;;AAEAoB,WAAMF,SAASlB,GAAT,CAAN;AACD;;AAED;AACA,OAAI,CAACoB,IAAIK,IAAT,EAAe;AACb,SAAI,cAAcD,IAAd,CAAmBJ,IAAIL,QAAvB,CAAJ,EAAsC;AACpCK,WAAIK,IAAJ,GAAW,IAAX;AACD,MAFD,MAEO,IAAI,eAAeD,IAAf,CAAoBJ,IAAIL,QAAxB,CAAJ,EAAuC;AAC5CK,WAAIK,IAAJ,GAAW,KAAX;AACD;AACF;;AAEDL,OAAId,IAAJ,GAAWc,IAAId,IAAJ,IAAY,GAAvB;;AAEA,OAAIoB,OAAON,IAAIE,IAAJ,CAASK,OAAT,CAAiB,GAAjB,MAA0B,CAAC,CAAtC;AACA,OAAIL,OAAOI,OAAO,MAAMN,IAAIE,IAAV,GAAiB,GAAxB,GAA8BF,IAAIE,IAA7C;;AAEA;AACAF,OAAIf,EAAJ,GAASe,IAAIL,QAAJ,GAAe,KAAf,GAAuBO,IAAvB,GAA8B,GAA9B,GAAoCF,IAAIK,IAAjD;AACA;AACAL,OAAIQ,IAAJ,GAAWR,IAAIL,QAAJ,GAAe,KAAf,GAAuBO,IAAvB,IAA+BH,OAAOA,IAAIM,IAAJ,KAAaL,IAAIK,IAAxB,GAA+B,EAA/B,GAAqC,MAAML,IAAIK,IAA9E,CAAX;;AAEA,UAAOL,GAAP;AACD,E;;;;;;AC1ED;AACA;AACA;AACA;AACA;AACA;;AAEA,0GAAyG,IAAI,GAAG,IAAI,SAAS,IAAI;;AAEjI;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,yEAAwE;AACxE;;AAEA;AACA,iBAAgB;AAChB;;AAEA;AACA;AACA;;AAEA;AACA;AACA,yEAAwE;AACxE,mFAAkF;AAClF;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA,oBAAmB,IAAI;AACvB;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAK;;AAEL;AACA;;;;;;;;;AClEAzB,QAAOC,OAAP,GAAiB,YAAY;AAAE,UAAO,YAAY,CAAE,CAArB;AAAwB,EAAvD,C;;;;;;;ACAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,YAAW,OAAO;AAClB,YAAW,SAAS;AACpB;AACA;AACA;;AAEA;;;AAGA;AACA;AACA,IAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,YAAW,OAAO;AAClB,aAAY,OAAO;AACnB;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAK;AACL;AACA;AACA;;;AAGA;AACA;;AAEA;AACA;AACA;AACA,IAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,YAAW,OAAO;AAClB,aAAY,OAAO;AACnB;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA,2BAA0B;AAC1B,uBAAsB;AACtB;;AAEA;AACA;;AAEA;AACA;AACA;AACA,aAAY,OAAO;AACnB;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,YAAW,OAAO;AAClB,aAAY,OAAO;AACnB;AACA;;AAEA;AACA;AACA;AACA;AACA,sFAAqF;AACrF;;AAEA;AACA;AACA;AACA;AACA,MAAK,OAAO;AACZ;AACA;AACA,IAAG,qCAAqC;AACxC;AACA;AACA,MAAK;AACL;AACA,oBAAmB;AACnB;AACA;AACA;AACA;AACA,IAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA,YAAW,OAAO;AAClB,aAAY,OAAO;AACnB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAG;AACH;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAK;AACL;AACA;AACA;;;AAGA;AACA;;AAEA;AACA;AACA;AACA,IAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,YAAW,OAAO;AAClB,aAAY,oBAAoB;AAChC;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,YAAW,qBAAqB;AAChC,aAAY,cAAc;AAC1B;AACA;AACA;;AAEA;AACA;AACA,4DAA2D;AAC3D;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AC7ZA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,YAAW,OAAO;AAClB,aAAY;AACZ;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,YAAW,OAAO;AAClB,YAAW,SAAS;AACpB,aAAY;AACZ;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,YAAW,OAAO;AAClB,YAAW,SAAS;AACpB,aAAY;AACZ;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,YAAW,OAAO;AAClB,YAAW,SAAS;AACpB,aAAY;AACZ;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,kBAAiB,sBAAsB;AACvC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,YAAW,OAAO;AAClB,YAAW,MAAM;AACjB,aAAY;AACZ;;AAEA;AACA;;AAEA;AACA;;AAEA,kBAAiB,sBAAsB;AACvC;AACA;;AAEA;AACA;AACA,4CAA2C,SAAS;AACpD;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,YAAW,OAAO;AAClB,aAAY;AACZ;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,YAAW,OAAO;AAClB,aAAY;AACZ;AACA;;AAEA;AACA;AACA;;;;;;;AC9KA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,YAAW,OAAO;AAClB,aAAY,OAAO;AACnB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,qCAAoC;AACpC,WAAU;AACV;;AAEA;AACA;;AAEA;AACA,wBAAuB;AACvB;AACA;AACA,IAAG;AACH;AACA,oBAAmB,iBAAiB;AACpC;AACA;AACA;AACA,IAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,YAAW,OAAO;AAClB,YAAW,MAAM;AACjB,aAAY,OAAO;AACnB;AACA;;AAEA;AACA;AACA,kCAAiC;AACjC;AACA;;AAEA;AACA;;AAEA;AACA,8BAA6B;AAC7B,IAAG;AACH,oBAAmB,iBAAiB;AACpC;AACA;AACA,IAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,YAAW,OAAO;AAClB,YAAW,SAAS;AACpB;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,uCAAsC;AACtC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,yCAAwC;AACxC,MAAK,yBAAyB;AAC9B,sBAAqB,gBAAgB;AACrC;AACA;AACA,MAAK,mDAAmD;AACxD;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AC5IA,kBAAiB;;AAEjB;AACA;AACA;;;;;;;;ACHA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;;;;;;;;;;AClBA;;;;AAIA,KAAIiC,MAAM,mBAAAtC,CAAQ,EAAR,CAAV;AACA,KAAI0B,SAAS,mBAAA1B,CAAQ,EAAR,CAAb;AACA,KAAIuC,UAAU,mBAAAvC,CAAQ,CAAR,CAAd;AACA,KAAIC,SAAS,mBAAAD,CAAQ,CAAR,CAAb;AACA,KAAIwC,KAAK,mBAAAxC,CAAQ,EAAR,CAAT;AACA,KAAIyC,OAAO,mBAAAzC,CAAQ,EAAR,CAAX;AACA,KAAIG,QAAQ,mBAAAH,CAAQ,CAAR,EAAiB,0BAAjB,CAAZ;AACA,KAAIoC,UAAU,mBAAApC,CAAQ,EAAR,CAAd;AACA,KAAI0C,UAAU,mBAAA1C,CAAQ,EAAR,CAAd;;AAEA;;;;AAIA,KAAI2C,MAAMC,OAAOC,SAAP,CAAiBC,cAA3B;;AAEA;;;;AAIA1C,QAAOC,OAAP,GAAiBH,OAAjB;;AAEA;;;;;;;;AAQA,UAASA,OAAT,CAAkBO,GAAlB,EAAuBC,IAAvB,EAA6B;AAC3B,OAAI,EAAE,gBAAgBR,OAAlB,CAAJ,EAAgC,OAAO,IAAIA,OAAJ,CAAYO,GAAZ,EAAiBC,IAAjB,CAAP;AAChC,OAAID,OAAQ,qBAAoBA,GAApB,yCAAoBA,GAApB,EAAZ,EAAsC;AACpCC,YAAOD,GAAP;AACAA,WAAME,SAAN;AACD;AACDD,UAAOA,QAAQ,EAAf;;AAEAA,QAAKK,IAAL,GAAYL,KAAKK,IAAL,IAAa,YAAzB;AACA,QAAKE,IAAL,GAAY,EAAZ;AACA,QAAK8B,IAAL,GAAY,EAAZ;AACA,QAAKrC,IAAL,GAAYA,IAAZ;AACA,QAAKsC,YAAL,CAAkBtC,KAAKsC,YAAL,KAAsB,KAAxC;AACA,QAAKC,oBAAL,CAA0BvC,KAAKuC,oBAAL,IAA6BC,QAAvD;AACA,QAAKC,iBAAL,CAAuBzC,KAAKyC,iBAAL,IAA0B,IAAjD;AACA,QAAKC,oBAAL,CAA0B1C,KAAK0C,oBAAL,IAA6B,IAAvD;AACA,QAAKC,mBAAL,CAAyB3C,KAAK2C,mBAAL,IAA4B,GAArD;AACA,QAAKC,OAAL,GAAe,IAAIZ,OAAJ,CAAY;AACzBa,UAAK,KAAKJ,iBAAL,EADoB;AAEzBK,UAAK,KAAKJ,oBAAL,EAFoB;AAGzBK,aAAQ,KAAKJ,mBAAL;AAHiB,IAAZ,CAAf;AAKA,QAAKK,OAAL,CAAa,QAAQhD,KAAKgD,OAAb,GAAuB,KAAvB,GAA+BhD,KAAKgD,OAAjD;AACA,QAAKC,UAAL,GAAkB,QAAlB;AACA,QAAKlD,GAAL,GAAWA,GAAX;AACA,QAAKmD,UAAL,GAAkB,EAAlB;AACA,QAAKC,QAAL,GAAgB,IAAhB;AACA,QAAKC,QAAL,GAAgB,KAAhB;AACA,QAAKC,YAAL,GAAoB,EAApB;AACA,OAAIC,UAAUtD,KAAKT,MAAL,IAAeA,MAA7B;AACA,QAAKgE,OAAL,GAAe,IAAID,QAAQE,OAAZ,EAAf;AACA,QAAKC,OAAL,GAAe,IAAIH,QAAQI,OAAZ,EAAf;AACA,QAAKC,WAAL,GAAmB3D,KAAK2D,WAAL,KAAqB,KAAxC;AACA,OAAI,KAAKA,WAAT,EAAsB,KAAKC,IAAL;AACvB;;AAED;;;;;;AAMApE,SAAQ2C,SAAR,CAAkB0B,OAAlB,GAA4B,YAAY;AACtC,QAAKC,IAAL,CAAUC,KAAV,CAAgB,IAAhB,EAAsBC,SAAtB;AACA,QAAK,IAAIC,GAAT,IAAgB,KAAK1D,IAArB,EAA2B;AACzB,SAAI0B,IAAIiC,IAAJ,CAAS,KAAK3D,IAAd,EAAoB0D,GAApB,CAAJ,EAA8B;AAC5B,YAAK1D,IAAL,CAAU0D,GAAV,EAAeH,IAAf,CAAoBC,KAApB,CAA0B,KAAKxD,IAAL,CAAU0D,GAAV,CAA1B,EAA0CD,SAA1C;AACD;AACF;AACF,EAPD;;AASA;;;;;;AAMAxE,SAAQ2C,SAAR,CAAkBgC,eAAlB,GAAoC,YAAY;AAC9C,QAAK,IAAIF,GAAT,IAAgB,KAAK1D,IAArB,EAA2B;AACzB,SAAI0B,IAAIiC,IAAJ,CAAS,KAAK3D,IAAd,EAAoB0D,GAApB,CAAJ,EAA8B;AAC5B,YAAK1D,IAAL,CAAU0D,GAAV,EAAe7D,EAAf,GAAoB,KAAKgE,UAAL,CAAgBH,GAAhB,CAApB;AACD;AACF;AACF,EAND;;AAQA;;;;;;;;AAQAzE,SAAQ2C,SAAR,CAAkBiC,UAAlB,GAA+B,UAAUH,GAAV,EAAe;AAC5C,UAAO,CAACA,QAAQ,GAAR,GAAc,EAAd,GAAoBA,MAAM,GAA3B,IAAmC,KAAKI,MAAL,CAAYjE,EAAtD;AACD,EAFD;;AAIA;;;;AAIAyB,SAAQrC,QAAQ2C,SAAhB;;AAEA;;;;;;;;AAQA3C,SAAQ2C,SAAR,CAAkBG,YAAlB,GAAiC,UAAUgC,CAAV,EAAa;AAC5C,OAAI,CAACN,UAAUO,MAAf,EAAuB,OAAO,KAAKC,aAAZ;AACvB,QAAKA,aAAL,GAAqB,CAAC,CAACF,CAAvB;AACA,UAAO,IAAP;AACD,EAJD;;AAMA;;;;;;;;AAQA9E,SAAQ2C,SAAR,CAAkBI,oBAAlB,GAAyC,UAAU+B,CAAV,EAAa;AACpD,OAAI,CAACN,UAAUO,MAAf,EAAuB,OAAO,KAAKE,qBAAZ;AACvB,QAAKA,qBAAL,GAA6BH,CAA7B;AACA,UAAO,IAAP;AACD,EAJD;;AAMA;;;;;;;;AAQA9E,SAAQ2C,SAAR,CAAkBM,iBAAlB,GAAsC,UAAU6B,CAAV,EAAa;AACjD,OAAI,CAACN,UAAUO,MAAf,EAAuB,OAAO,KAAKG,kBAAZ;AACvB,QAAKA,kBAAL,GAA0BJ,CAA1B;AACA,QAAK1B,OAAL,IAAgB,KAAKA,OAAL,CAAa+B,MAAb,CAAoBL,CAApB,CAAhB;AACA,UAAO,IAAP;AACD,EALD;;AAOA9E,SAAQ2C,SAAR,CAAkBQ,mBAAlB,GAAwC,UAAU2B,CAAV,EAAa;AACnD,OAAI,CAACN,UAAUO,MAAf,EAAuB,OAAO,KAAKK,oBAAZ;AACvB,QAAKA,oBAAL,GAA4BN,CAA5B;AACA,QAAK1B,OAAL,IAAgB,KAAKA,OAAL,CAAaiC,SAAb,CAAuBP,CAAvB,CAAhB;AACA,UAAO,IAAP;AACD,EALD;;AAOA;;;;;;;;AAQA9E,SAAQ2C,SAAR,CAAkBO,oBAAlB,GAAyC,UAAU4B,CAAV,EAAa;AACpD,OAAI,CAACN,UAAUO,MAAf,EAAuB,OAAO,KAAKO,qBAAZ;AACvB,QAAKA,qBAAL,GAA6BR,CAA7B;AACA,QAAK1B,OAAL,IAAgB,KAAKA,OAAL,CAAamC,MAAb,CAAoBT,CAApB,CAAhB;AACA,UAAO,IAAP;AACD,EALD;;AAOA;;;;;;;AAOA9E,SAAQ2C,SAAR,CAAkBa,OAAlB,GAA4B,UAAUsB,CAAV,EAAa;AACvC,OAAI,CAACN,UAAUO,MAAf,EAAuB,OAAO,KAAKS,QAAZ;AACvB,QAAKA,QAAL,GAAgBV,CAAhB;AACA,UAAO,IAAP;AACD,EAJD;;AAMA;;;;;;;AAOA9E,SAAQ2C,SAAR,CAAkB8C,oBAAlB,GAAyC,YAAY;AACnD;AACA,OAAI,CAAC,KAAKC,YAAN,IAAsB,KAAKV,aAA3B,IAA4C,KAAK5B,OAAL,CAAauC,QAAb,KAA0B,CAA1E,EAA6E;AAC3E;AACA,UAAKC,SAAL;AACD;AACF,EAND;;AAQA;;;;;;;;AAQA5F,SAAQ2C,SAAR,CAAkByB,IAAlB,GACApE,QAAQ2C,SAAR,CAAkBpB,OAAlB,GAA4B,UAAUsE,EAAV,EAAcrF,IAAd,EAAoB;;AAE9C,OAAI,CAAC,KAAKiD,UAAL,CAAgBvB,OAAhB,CAAwB,MAAxB,CAAL,EAAsC,OAAO,IAAP;;AAGtC,QAAK2C,MAAL,GAAczC,IAAI,KAAK7B,GAAT,EAAc,KAAKC,IAAnB,CAAd;AACA,OAAIa,SAAS,KAAKwD,MAAlB;AACA,OAAIiB,OAAO,IAAX;AACA,QAAKrC,UAAL,GAAkB,SAAlB;AACA,QAAKsC,aAAL,GAAqB,KAArB;;AAEA;AACA,OAAIC,UAAU1D,GAAGjB,MAAH,EAAW,MAAX,EAAmB,YAAY;AAC3CyE,UAAKG,MAAL;AACAJ,WAAMA,IAAN;AACD,IAHa,CAAd;;AAKA;AACA,OAAIK,WAAW5D,GAAGjB,MAAH,EAAW,OAAX,EAAoB,UAAU8E,IAAV,EAAgB;;AAEjDL,UAAKM,OAAL;AACAN,UAAKrC,UAAL,GAAkB,QAAlB;AACAqC,UAAKzB,OAAL,CAAa,eAAb,EAA8B8B,IAA9B;AACA,SAAIN,EAAJ,EAAQ;AACN,WAAIQ,MAAM,IAAIC,KAAJ,CAAU,kBAAV,CAAV;AACAD,WAAIF,IAAJ,GAAWA,IAAX;AACAN,UAAGQ,GAAH;AACD,MAJD,MAIO;AACL;AACAP,YAAKL,oBAAL;AACD;AACF,IAbc,CAAf;;AAeA;AACA,OAAI,UAAU,KAAKD,QAAnB,EAA6B;AAC3B,SAAIhC,UAAU,KAAKgC,QAAnB;;AAGA,SAAIhC,YAAY,CAAhB,EAAmB;AACjBwC,eAAQO,OAAR,GADiB,CACE;AACpB;;AAED;AACA,SAAIC,QAAQC,WAAW,YAAY;;AAEjCT,eAAQO,OAAR;AACAlF,cAAOqF,KAAP;AACArF,cAAOiD,IAAP,CAAY,OAAZ,EAAqB,SAArB;AACAwB,YAAKzB,OAAL,CAAa,iBAAb,EAAgCb,OAAhC;AACD,MANW,EAMTA,OANS,CAAZ;;AAQA,UAAKX,IAAL,CAAU8D,IAAV,CAAe;AACbJ,gBAAS,mBAAY;AACnBK,sBAAaJ,KAAb;AACD;AAHY,MAAf;AAKD;;AAED,QAAK3D,IAAL,CAAU8D,IAAV,CAAeX,OAAf;AACA,QAAKnD,IAAL,CAAU8D,IAAV,CAAeT,QAAf;;AAEA,UAAO,IAAP;AACD,EA/DD;;AAiEA;;;;;;AAMAlG,SAAQ2C,SAAR,CAAkBsD,MAAlB,GAA2B,YAAY;;AAGrC;AACA,QAAKG,OAAL;;AAEA;AACA,QAAK3C,UAAL,GAAkB,MAAlB;AACA,QAAKa,IAAL,CAAU,MAAV;;AAEA;AACA,OAAIjD,SAAS,KAAKwD,MAAlB;AACA,QAAKhC,IAAL,CAAU8D,IAAV,CAAerE,GAAGjB,MAAH,EAAW,MAAX,EAAmBkB,KAAK,IAAL,EAAW,QAAX,CAAnB,CAAf;AACA,QAAKM,IAAL,CAAU8D,IAAV,CAAerE,GAAGjB,MAAH,EAAW,MAAX,EAAmBkB,KAAK,IAAL,EAAW,QAAX,CAAnB,CAAf;AACA,QAAKM,IAAL,CAAU8D,IAAV,CAAerE,GAAGjB,MAAH,EAAW,MAAX,EAAmBkB,KAAK,IAAL,EAAW,QAAX,CAAnB,CAAf;AACA,QAAKM,IAAL,CAAU8D,IAAV,CAAerE,GAAGjB,MAAH,EAAW,OAAX,EAAoBkB,KAAK,IAAL,EAAW,SAAX,CAApB,CAAf;AACA,QAAKM,IAAL,CAAU8D,IAAV,CAAerE,GAAGjB,MAAH,EAAW,OAAX,EAAoBkB,KAAK,IAAL,EAAW,SAAX,CAApB,CAAf;AACA,QAAKM,IAAL,CAAU8D,IAAV,CAAerE,GAAG,KAAK2B,OAAR,EAAiB,SAAjB,EAA4B1B,KAAK,IAAL,EAAW,WAAX,CAA5B,CAAf;AACD,EAlBD;;AAoBA;;;;;;AAMAvC,SAAQ2C,SAAR,CAAkBkE,MAAlB,GAA2B,YAAY;AACrC,QAAKlD,QAAL,GAAgB,IAAImD,IAAJ,EAAhB;AACA,QAAKzC,OAAL,CAAa,MAAb;AACD,EAHD;;AAKA;;;;;;AAMArE,SAAQ2C,SAAR,CAAkBoE,MAAlB,GAA2B,YAAY;AACrC,QAAK1C,OAAL,CAAa,MAAb,EAAqB,IAAIyC,IAAJ,KAAa,KAAKnD,QAAvC;AACD,EAFD;;AAIA;;;;;;AAMA3D,SAAQ2C,SAAR,CAAkBqE,MAAlB,GAA2B,UAAUb,IAAV,EAAgB;AACzC,QAAKlC,OAAL,CAAagD,GAAb,CAAiBd,IAAjB;AACD,EAFD;;AAIA;;;;;;AAMAnG,SAAQ2C,SAAR,CAAkBuE,SAAlB,GAA8B,UAAUC,MAAV,EAAkB;AAC9C,QAAK7C,IAAL,CAAU,QAAV,EAAoB6C,MAApB;AACD,EAFD;;AAIA;;;;;;AAMAnH,SAAQ2C,SAAR,CAAkByE,OAAlB,GAA4B,UAAUf,GAAV,EAAe;;AAEzC,QAAKhC,OAAL,CAAa,OAAb,EAAsBgC,GAAtB;AACD,EAHD;;AAKA;;;;;;;AAOArG,SAAQ2C,SAAR,CAAkBtB,MAAlB,GAA2B,UAAUoD,GAAV,EAAejE,IAAf,EAAqB;AAC9C,OAAIa,SAAS,KAAKN,IAAL,CAAU0D,GAAV,CAAb;AACA,OAAI,CAACpD,MAAL,EAAa;AACXA,cAAS,IAAIG,MAAJ,CAAW,IAAX,EAAiBiD,GAAjB,EAAsBjE,IAAtB,CAAT;AACA,UAAKO,IAAL,CAAU0D,GAAV,IAAiBpD,MAAjB;AACA,SAAIyE,OAAO,IAAX;AACAzE,YAAOiB,EAAP,CAAU,YAAV,EAAwB+E,YAAxB;AACAhG,YAAOiB,EAAP,CAAU,SAAV,EAAqB,YAAY;AAC/BjB,cAAOT,EAAP,GAAYkF,KAAKlB,UAAL,CAAgBH,GAAhB,CAAZ;AACD,MAFD;;AAIA,SAAI,KAAKN,WAAT,EAAsB;AACpB;AACAkD;AACD;AACF;;AAED,YAASA,YAAT,GAAyB;AACvB,SAAI,CAAC,CAACnF,QAAQ4D,KAAKpC,UAAb,EAAyBrC,MAAzB,CAAN,EAAwC;AACtCyE,YAAKpC,UAAL,CAAgBiD,IAAhB,CAAqBtF,MAArB;AACD;AACF;;AAED,UAAOA,MAAP;AACD,EAxBD;;AA0BA;;;;;;AAMArB,SAAQ2C,SAAR,CAAkB4D,OAAlB,GAA4B,UAAUlF,MAAV,EAAkB;AAC5C,OAAIiG,QAAQpF,QAAQ,KAAKwB,UAAb,EAAyBrC,MAAzB,CAAZ;AACA,OAAI,CAACiG,KAAL,EAAY,KAAK5D,UAAL,CAAgB6D,MAAhB,CAAuBD,KAAvB,EAA8B,CAA9B;AACZ,OAAI,KAAK5D,UAAL,CAAgBqB,MAApB,EAA4B;;AAE5B,QAAK2B,KAAL;AACD,EAND;;AAQA;;;;;;;AAOA1G,SAAQ2C,SAAR,CAAkBwE,MAAlB,GAA2B,UAAUA,MAAV,EAAkB;;AAE3C,OAAIrB,OAAO,IAAX;AACA,OAAIqB,OAAO/F,KAAP,IAAgB+F,OAAOK,IAAP,KAAgB,CAApC,EAAuCL,OAAO1C,GAAP,IAAc,MAAM0C,OAAO/F,KAA3B;;AAEvC,OAAI,CAAC0E,KAAKlC,QAAV,EAAoB;AAClB;AACAkC,UAAKlC,QAAL,GAAgB,IAAhB;AACA,UAAKG,OAAL,CAAa0D,MAAb,CAAoBN,MAApB,EAA4B,UAAUO,cAAV,EAA0B;AACpD,YAAK,IAAIC,IAAI,CAAb,EAAgBA,IAAID,eAAe3C,MAAnC,EAA2C4C,GAA3C,EAAgD;AAC9C7B,cAAKjB,MAAL,CAAY+C,KAAZ,CAAkBF,eAAeC,CAAf,CAAlB,EAAqCR,OAAOU,OAA5C;AACD;AACD/B,YAAKlC,QAAL,GAAgB,KAAhB;AACAkC,YAAKgC,kBAAL;AACD,MAND;AAOD,IAVD,MAUO;AAAE;AACPhC,UAAKjC,YAAL,CAAkB8C,IAAlB,CAAuBQ,MAAvB;AACD;AACF,EAlBD;;AAoBA;;;;;;;AAOAnH,SAAQ2C,SAAR,CAAkBmF,kBAAlB,GAAuC,YAAY;AACjD,OAAI,KAAKjE,YAAL,CAAkBkB,MAAlB,GAA2B,CAA3B,IAAgC,CAAC,KAAKnB,QAA1C,EAAoD;AAClD,SAAImE,OAAO,KAAKlE,YAAL,CAAkBmE,KAAlB,EAAX;AACA,UAAKb,MAAL,CAAYY,IAAZ;AACD;AACF,EALD;;AAOA;;;;;;AAMA/H,SAAQ2C,SAAR,CAAkByD,OAAlB,GAA4B,YAAY;;AAGtC,OAAI6B,aAAa,KAAKpF,IAAL,CAAUkC,MAA3B;AACA,QAAK,IAAI4C,IAAI,CAAb,EAAgBA,IAAIM,UAApB,EAAgCN,GAAhC,EAAqC;AACnC,SAAIO,MAAM,KAAKrF,IAAL,CAAUmF,KAAV,EAAV;AACAE,SAAI3B,OAAJ;AACD;;AAED,QAAK1C,YAAL,GAAoB,EAApB;AACA,QAAKD,QAAL,GAAgB,KAAhB;AACA,QAAKD,QAAL,GAAgB,IAAhB;;AAEA,QAAKM,OAAL,CAAasC,OAAb;AACD,EAdD;;AAgBA;;;;;;AAMAvG,SAAQ2C,SAAR,CAAkB+D,KAAlB,GACA1G,QAAQ2C,SAAR,CAAkBwF,UAAlB,GAA+B,YAAY;;AAEzC,QAAKpC,aAAL,GAAqB,IAArB;AACA,QAAKL,YAAL,GAAoB,KAApB;AACA,OAAI,cAAc,KAAKjC,UAAvB,EAAmC;AACjC;AACA;AACA,UAAK2C,OAAL;AACD;AACD,QAAKhD,OAAL,CAAagF,KAAb;AACA,QAAK3E,UAAL,GAAkB,QAAlB;AACA,OAAI,KAAKoB,MAAT,EAAiB,KAAKA,MAAL,CAAY6B,KAAZ;AAClB,EAbD;;AAeA;;;;;;AAMA1G,SAAQ2C,SAAR,CAAkB0F,OAAlB,GAA4B,UAAUC,MAAV,EAAkB;;AAG5C,QAAKlC,OAAL;AACA,QAAKhD,OAAL,CAAagF,KAAb;AACA,QAAK3E,UAAL,GAAkB,QAAlB;AACA,QAAKa,IAAL,CAAU,OAAV,EAAmBgE,MAAnB;;AAEA,OAAI,KAAKtD,aAAL,IAAsB,CAAC,KAAKe,aAAhC,EAA+C;AAC7C,UAAKH,SAAL;AACD;AACF,EAXD;;AAaA;;;;;;AAMA5F,SAAQ2C,SAAR,CAAkBiD,SAAlB,GAA8B,YAAY;AACxC,OAAI,KAAKF,YAAL,IAAqB,KAAKK,aAA9B,EAA6C,OAAO,IAAP;;AAE7C,OAAID,OAAO,IAAX;;AAEA,OAAI,KAAK1C,OAAL,CAAauC,QAAb,IAAyB,KAAKV,qBAAlC,EAAyD;;AAEvD,UAAK7B,OAAL,CAAagF,KAAb;AACA,UAAK/D,OAAL,CAAa,kBAAb;AACA,UAAKqB,YAAL,GAAoB,KAApB;AACD,IALD,MAKO;AACL,SAAI6C,QAAQ,KAAKnF,OAAL,CAAaoF,QAAb,EAAZ;;AAGA,UAAK9C,YAAL,GAAoB,IAApB;AACA,SAAIc,QAAQC,WAAW,YAAY;AACjC,WAAIX,KAAKC,aAAT,EAAwB;;AAGxBD,YAAKzB,OAAL,CAAa,mBAAb,EAAkCyB,KAAK1C,OAAL,CAAauC,QAA/C;AACAG,YAAKzB,OAAL,CAAa,cAAb,EAA6ByB,KAAK1C,OAAL,CAAauC,QAA1C;;AAEA;AACA,WAAIG,KAAKC,aAAT,EAAwB;;AAExBD,YAAK1B,IAAL,CAAU,UAAUiC,GAAV,EAAe;AACvB,aAAIA,GAAJ,EAAS;;AAEPP,gBAAKJ,YAAL,GAAoB,KAApB;AACAI,gBAAKF,SAAL;AACAE,gBAAKzB,OAAL,CAAa,iBAAb,EAAgCgC,IAAIF,IAApC;AACD,UALD,MAKO;;AAELL,gBAAK2C,WAAL;AACD;AACF,QAVD;AAWD,MArBW,EAqBTF,KArBS,CAAZ;;AAuBA,UAAK1F,IAAL,CAAU8D,IAAV,CAAe;AACbJ,gBAAS,mBAAY;AACnBK,sBAAaJ,KAAb;AACD;AAHY,MAAf;AAKD;AACF,EA5CD;;AA8CA;;;;;;AAMAxG,SAAQ2C,SAAR,CAAkB8F,WAAlB,GAAgC,YAAY;AAC1C,OAAIC,UAAU,KAAKtF,OAAL,CAAauC,QAA3B;AACA,QAAKD,YAAL,GAAoB,KAApB;AACA,QAAKtC,OAAL,CAAagF,KAAb;AACA,QAAKzD,eAAL;AACA,QAAKN,OAAL,CAAa,WAAb,EAA0BqE,OAA1B;AACD,EAND,C;;;;;;;ACzjBA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACTA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,YAAW,cAAc;AACzB,YAAW,OAAO;AAClB;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,IAAG;AACH;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4FAA2F;;AAE3F;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,mCAAkC;;AAElC;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,YAAW,OAAO;AAClB,aAAY;AACZ;AACA;;AAEA;;AAEA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAG;;AAEH;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAG;AACH;AACA;AACA;AACA;AACA,MAAK;AACL;AACA,IAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,IAAG;AACH;AACA;AACA,IAAG;AACH;AACA;AACA,IAAG;AACH;AACA;AACA,IAAG;AACH;;AAEA;AACA;AACA;AACA,YAAW,OAAO;AAClB;AACA;;AAEA;;AAEA,+CAA8C,WAAW;AACzD;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAGA,sBAAqB,8BAA8B;AACnD;AACA;AACA;;AAEA;AACA;AACA;AACA;;;AAGA;AACA;AACA;;;AAGA;;AAEA;AACA,4BAA2B,kBAAkB;AAC7C;AACA;AACA;AACA;AACA,UAAS;AACT,QAAO;;AAEP;AACA;AACA;AACA;AACA,MAAK;AACL;;AAEA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;;;AAIA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,8CAA6C,OAAO;AACpD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAGA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAG;;AAEH;AACA;;AAEA;AACA;AACA;AACA,YAAW,OAAO;AAClB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,IAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,IAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,IAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,IAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,YAAW,OAAO;AAClB,YAAW,SAAS;AACpB,YAAW,OAAO;AAClB,aAAY,OAAO;AACnB;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,YAAW,OAAO;AAClB,YAAW,OAAO;AAClB,YAAW,OAAO;AAClB,YAAW,SAAS;AACpB;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,UAAS;AACT;AACA;AACA,QAAO;AACP,MAAK;AACL;AACA,MAAK;AACL;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,YAAW,MAAM;AACjB;AACA;AACA;;AAEA;AACA;AACA,uCAAsC,OAAO;AAC7C;AACA;AACA;AACA;;;;;;;AC3uBA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,IAAG;AACH;AACA;AACA;AACA;;;;;;;ACpDA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAG,YAAY;;AAEf;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAG,YAAY;;AAEf;AACA;AACA;AACA,MAAK,YAAY;AACjB;AACA;;;;;;;;ACpCA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,EAAC;AACD;AACA;AACA;AACA;;;;;;;AChBA;AACA;AACA;AACA,IAAG;AACH;AACA,IAAG;AACH,sCAAqC;AACrC;AACA,EAAC;;;;;;;ACRD;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,YAAW,OAAO;AAClB;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,YAAW,OAAO;AAClB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,YAAW,OAAO;AAClB,YAAW,SAAS;AACpB;AACA;;AAEA;AACA;AACA,2BAA0B,iDAAiD;AAC3E;AACA;AACA;AACA;AACA,IAAG;AACH;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,IAAG;AACH;AACA;AACA,IAAG;AACH;AACA;;AAEA;AACA;AACA;AACA,YAAW,OAAO;AAClB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,eAAc;;AAEd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAK;;AAEL;AACA;AACA;AACA;AACA,UAAS;AACT,4DAA2D;AAC3D;AACA,QAAO;AACP;;AAEA;AACA;AACA,MAAK;;AAEL;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAK;AACL;AACA;AACA;AACA;AACA,gIAA+H;AAC/H;AACA;AACA,YAAW;AACX;AACA;AACA;AACA;AACA,UAAS;AACT;AACA;AACA;AACA;AACA,YAAW;AACX;AACA;AACA;;;AAGA;AACA,IAAG;AACH;AACA;AACA;AACA;AACA;AACA,MAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAG;AACH;AACA;;AAEA;AACA;AACA;AACA,MAAK;AACL;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAK;AACL,iGAAgG;AAChG;AACA,MAAK;AACL;AACA;AACA,IAAG;AACH;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,IAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACjaA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,iCAAgC,iBAAiB;AACjD;AACA,EAAC;;AAED;AACA;AACA;AACA,YAAW,OAAO;AAClB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,YAAW,SAAS;AACpB;AACA;;AAEA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA,QAAO;AACP;;AAEA;;AAEA;AACA;;AAEA;AACA,QAAO;AACP;AACA,IAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,MAAK;;AAEL;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA,kBAAiB,gBAAgB;AACjC;;AAEA;;AAEA;AACA,IAAG;AACH;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,YAAW,MAAM;AACjB,YAAW,SAAS;AACpB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,IAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;;;;;ACpPA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,YAAW,OAAO;AAClB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,YAAW,OAAO;AAClB,aAAY,UAAU;AACtB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,YAAW,MAAM;AACjB;AACA;;AAEA;AACA;AACA;AACA,IAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,YAAW,OAAO;AAClB;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;;;;;;AChKA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA,YAAW;;AAEX;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,IAAG;AACH;AACA;;AAEA,+BAA8B;AAC9B;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,+DAA8D,gBAAgB;AAC9E;;AAEA;;AAEA;;AAEA;AACA,gCAA+B;AAC/B;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,kBAAiB,yBAAyB;AAC1C;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,2BAA0B,qCAAqC;AAC/D;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,YAAW,OAAO;AAClB,aAAY,OAAO;AACnB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAG;AACH;AACA;AACA;AACA,oBAAmB,kBAAkB;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,aAAY,OAAO;AACnB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,eAAc;AACd,MAAK;AACL,eAAc;AACd;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,WAAU;AACV;;AAEA;AACA;AACA,+BAA8B,gBAAgB;AAC9C,IAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,YAAW,OAAO;AAClB,aAAY,OAAO;AACnB;;AAEA;AACA;AACA;AACA,aAAY,oBAAoB,oCAAoC;AACpE;;AAEA;;AAEA;AACA;AACA;;AAEA,WAAU;AACV;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAW,MAAM;AACjB;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAK;AACL;;AAEA;AACA;AACA,IAAG;AACH;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAK;AACL;;AAEA,kBAAiB,gBAAgB;AACjC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,YAAW,OAAO;AAClB;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA,mCAAkC,OAAO;AACzC;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAW,MAAM;AACjB,aAAY,YAAY;AACxB;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAK;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA,QAAO;AACP;AACA;AACA,oDAAmD;AACnD,MAAK;;AAEL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAuB,cAAc;AACrC;AACA;AACA;AACA;;AAEA,sBAAqB;AACrB;AACA,QAAO,OAAO;AACd;AACA;;AAEA;AACA,sBAAqB,mBAAmB;AACxC;AACA;AACA;;AAEA;AACA,sBAAqB,iBAAiB;AACtC;AACA;AACA,MAAK;;AAEL;AACA,IAAG;AACH;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAuB,oBAAoB;AAC3C;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,sBAAqB,mBAAmB;AACxC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAK;AACL;;AAEA;AACA;AACA,IAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA,YAAW,YAAY;AACvB;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,oBAAmB,EAAE;AACrB;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,QAAO;AACP;AACA;AACA;AACA,wBAAuB,kBAAkB;AACzC;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAG;AACH;;;;;;;;AC3lBA;AACA;AACA;AACA,aAAY,MAAM;AAClB;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AClBA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA,YAAW,OAAO;AAClB;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,oCAAmC,OAAO;AAC1C;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;;;;;;AC/DA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,2BAA0B,sCAAsC;;AAEhE,mBAAkB,gBAAgB;AAClC,iBAAgB,cAAc;AAC9B,qBAAoB,aAAa;;AAEjC;AACA;AACA;;AAEA;AACA;AACA,8BAA6B,SAAS;AACtC;AACA;AACA;AACA;;;;;;;AC5BA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,UAAS;AACT;AACA;AACA;AACA;;AAEA;;;;;;;AC3BA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAoC;AACpC;AACA,KAAI;AACJ,4BAA2B;AAC3B;AACA;AACA;AACA;AACA,IAAG;AACH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,sCAAqC;AACrC;AACA;AACA;AACA,sCAAqC;AACrC;AACA;AACA,2CAA0C;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA,2CAA0C;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,IAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;;;;;ACjNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,gBAAe,SAAS;AACxB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,MAAK;AACL;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,gBAAe,SAAS;AACxB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,EAAC;;;;;;;AC1DD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,IAAG;AACH;AACA;AACA,EAAC;;AAED;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,IAAG;AACH;AACA;AACA,EAAC;;AAED;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,IAAG;AACH;;AAEA;AACA;;AAEA;AACA;AACA;AACA,IAAG;;AAEH;AACA;;AAEA;AACA,0DAAyD;AACzD;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAG;AACH;AACA,IAAG;AACH;AACA;AACA,EAAC;;;;;;;ACnGD;AACA;AACA;AACA;AACA,YAAW;AACX;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,YAAW,OAAO;AAClB;AACA;;AAEA;AACA;AACA;AACA,oCAAmC,OAAO;AAC1C;AACA;AACA;AACA;AACA;;;;;;;;ACnCA;AACA;AACA;AACA;AACA;AACA,G;;;;;;ACNA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,YAAW,OAAO;AAClB,cAAa,OAAO;AACpB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAG;;AAEH;AACA;;AAEA;AACA;AACA;AACA,YAAW,OAAO;AAClB,cAAa,OAAO;AACpB;AACA;AACA;AACA;;AAEA,cAAa,gBAAgB;AAC7B;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,cAAa,OAAO;AACpB;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,OAAM,YAAY;;AAElB;AACA;AACA;AACA;AACA;AACA;;;;;;;ACnEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA,oBAAmB;;AAEnB;AACA;AACA;AACA,YAAW,OAAO;AAClB;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,IAAG;;AAEH;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAK;AACL;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAG;AACH;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAK;AACL;AACA;;AAEA;AACA;AACA;AACA,YAAW,OAAO;AAClB,YAAW,SAAS;AACpB;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,QAAO;AACP;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAK;AACL;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,IAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA,IAAG;AACH;AACA;AACA;;;;;;;ACrOA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,EAAC;AACD;AACA;;AAEA;AACA;AACA;AACA,IAAG,YAAY;AACf;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,UAAS,OAAO;AAChB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAG;AACH;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAG;AACH;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,YAAW,MAAM;AACjB;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,6BAA4B,OAAO;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAW;AACX;AACA;AACA,UAAS;;AAET;;AAEA;AACA,QAAO;AACP,MAAK;AACL;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,aAAY,QAAQ;AACpB;AACA;;AAEA;AACA;AACA;;;;;;;AC1SA,gB;;;;;;;ACCA;;AAEA;AACA;AACA,kBAAiB,gBAAgB;AACjC;AACA;AACA;AACA,G;;;;;;;;;;ACRA;;;;AAIA,KAAI3I,SAAS,mBAAAD,CAAQ,CAAR,CAAb;AACA,KAAIuC,UAAU,mBAAAvC,CAAQ,CAAR,CAAd;AACA,KAAI6I,UAAU,mBAAA7I,CAAQ,EAAR,CAAd;AACA,KAAIwC,KAAK,mBAAAxC,CAAQ,EAAR,CAAT;AACA,KAAIyC,OAAO,mBAAAzC,CAAQ,EAAR,CAAX;AACA,KAAIG,QAAQ,mBAAAH,CAAQ,CAAR,EAAiB,yBAAjB,CAAZ;AACA,KAAI8I,UAAU,mBAAA9I,CAAQ,EAAR,CAAd;AACA,KAAI+I,SAAS,mBAAA/I,CAAQ,EAAR,CAAb;;AAEA;;;;AAIAI,QAAOC,OAAP,GAAiBA,UAAUqB,MAA3B;;AAEA;;;;;;;AAOA,KAAIsH,SAAS;AACXvH,YAAS,CADE;AAEXwH,kBAAe,CAFJ;AAGXC,oBAAiB,CAHN;AAIXtF,eAAY,CAJD;AAKXyE,eAAY,CALD;AAMXc,UAAO,CANI;AAOXrD,cAAW,CAPA;AAQXsD,sBAAmB,CARR;AASXC,qBAAkB,CATP;AAUXC,oBAAiB,CAVN;AAWX1D,iBAAc,CAXH;AAYX2D,SAAM,CAZK;AAaXC,SAAM;AAbK,EAAb;;AAgBA;;;;AAIA,KAAIhF,OAAOjC,QAAQM,SAAR,CAAkB2B,IAA7B;;AAEA;;;;;;AAMA,UAAS9C,MAAT,CAAiBL,EAAjB,EAAqBsD,GAArB,EAA0BjE,IAA1B,EAAgC;AAC9B,QAAKW,EAAL,GAAUA,EAAV;AACA,QAAKsD,GAAL,GAAWA,GAAX;AACA,QAAK8E,IAAL,GAAY,IAAZ,CAH8B,CAGZ;AAClB,QAAKC,GAAL,GAAW,CAAX;AACA,QAAKC,IAAL,GAAY,EAAZ;AACA,QAAKC,aAAL,GAAqB,EAArB;AACA,QAAKC,UAAL,GAAkB,EAAlB;AACA,QAAKC,SAAL,GAAiB,KAAjB;AACA,QAAKC,YAAL,GAAoB,IAApB;AACA,QAAKC,KAAL,GAAa,EAAb;AACA,OAAItJ,QAAQA,KAAKY,KAAjB,EAAwB;AACtB,UAAKA,KAAL,GAAaZ,KAAKY,KAAlB;AACD;AACD,OAAI,KAAKD,EAAL,CAAQgD,WAAZ,EAAyB,KAAKC,IAAL;AAC1B;;AAED;;;;AAIA/B,SAAQb,OAAOmB,SAAf;;AAEA;;;;;;AAMAnB,QAAOmB,SAAP,CAAiBoH,SAAjB,GAA6B,YAAY;AACvC,OAAI,KAAKlH,IAAT,EAAe;;AAEf,OAAI1B,KAAK,KAAKA,EAAd;AACA,QAAK0B,IAAL,GAAY,CACVP,GAAGnB,EAAH,EAAO,MAAP,EAAeoB,KAAK,IAAL,EAAW,QAAX,CAAf,CADU,EAEVD,GAAGnB,EAAH,EAAO,QAAP,EAAiBoB,KAAK,IAAL,EAAW,UAAX,CAAjB,CAFU,EAGVD,GAAGnB,EAAH,EAAO,OAAP,EAAgBoB,KAAK,IAAL,EAAW,SAAX,CAAhB,CAHU,CAAZ;AAKD,EATD;;AAWA;;;;;;AAMAf,QAAOmB,SAAP,CAAiByB,IAAjB,GACA5C,OAAOmB,SAAP,CAAiBpB,OAAjB,GAA2B,YAAY;AACrC,OAAI,KAAKqI,SAAT,EAAoB,OAAO,IAAP;;AAEpB,QAAKG,SAAL;AACA,OAAI,CAAC,KAAK5I,EAAL,CAAQuE,YAAb,EAA2B,KAAKvE,EAAL,CAAQiD,IAAR,GAJU,CAIM;AAC3C,OAAI,WAAW,KAAKjD,EAAL,CAAQsC,UAAvB,EAAmC,KAAKwC,MAAL;AACnC,QAAK3B,IAAL,CAAU,YAAV;AACA,UAAO,IAAP;AACD,EATD;;AAWA;;;;;;;AAOA9C,QAAOmB,SAAP,CAAiBqH,IAAjB,GAAwB,YAAY;AAClC,OAAIC,OAAOtB,QAAQnE,SAAR,CAAX;AACAyF,QAAKC,OAAL,CAAa,SAAb;AACA,QAAK5F,IAAL,CAAUC,KAAV,CAAgB,IAAhB,EAAsB0F,IAAtB;AACA,UAAO,IAAP;AACD,EALD;;AAOA;;;;;;;;;AASAzI,QAAOmB,SAAP,CAAiB2B,IAAjB,GAAwB,UAAU6F,EAAV,EAAc;AACpC,OAAIrB,OAAOlG,cAAP,CAAsBuH,EAAtB,CAAJ,EAA+B;AAC7B7F,UAAKC,KAAL,CAAW,IAAX,EAAiBC,SAAjB;AACA,YAAO,IAAP;AACD;;AAED,OAAIyF,OAAOtB,QAAQnE,SAAR,CAAX;AACA,OAAI2C,SAAS;AACXK,WAAM,CAAC,KAAKsC,KAAL,CAAWM,MAAX,KAAsB3J,SAAtB,GAAkC,KAAKqJ,KAAL,CAAWM,MAA7C,GAAsDvB,OAAOoB,IAAP,CAAvD,IAAuElK,OAAOsK,YAA9E,GAA6FtK,OAAOuK,KAD/F;AAEXnE,WAAM8D;AAFK,IAAb;;AAKA9C,UAAOU,OAAP,GAAiB,EAAjB;AACAV,UAAOU,OAAP,CAAe0C,QAAf,GAA0B,CAAC,KAAKT,KAAN,IAAe,UAAU,KAAKA,KAAL,CAAWS,QAA9D;;AAEA;AACA,OAAI,eAAe,OAAON,KAAKA,KAAKlF,MAAL,GAAc,CAAnB,CAA1B,EAAiD;;AAE/C,UAAK0E,IAAL,CAAU,KAAKD,GAAf,IAAsBS,KAAKO,GAAL,EAAtB;AACArD,YAAOvG,EAAP,GAAY,KAAK4I,GAAL,EAAZ;AACD;;AAED,OAAI,KAAKI,SAAT,EAAoB;AAClB,UAAKzC,MAAL,CAAYA,MAAZ;AACD,IAFD,MAEO;AACL,UAAKwC,UAAL,CAAgBhD,IAAhB,CAAqBQ,MAArB;AACD;;AAED,QAAK2C,KAAL,GAAa,EAAb;;AAEA,UAAO,IAAP;AACD,EA/BD;;AAiCA;;;;;;;AAOAtI,QAAOmB,SAAP,CAAiBwE,MAAjB,GAA0B,UAAUA,MAAV,EAAkB;AAC1CA,UAAO1C,GAAP,GAAa,KAAKA,GAAlB;AACA,QAAKtD,EAAL,CAAQgG,MAAR,CAAeA,MAAf;AACD,EAHD;;AAKA;;;;;;AAMA3F,QAAOmB,SAAP,CAAiBsD,MAAjB,GAA0B,YAAY;;AAGpC;AACA,OAAI,QAAQ,KAAKxB,GAAjB,EAAsB;AACpB,SAAI,KAAKrD,KAAT,EAAgB;AACd,WAAIA,QAAQ,QAAO,KAAKA,KAAZ,MAAsB,QAAtB,GAAiCwH,QAAQnB,MAAR,CAAe,KAAKrG,KAApB,CAAjC,GAA8D,KAAKA,KAA/E;;AAEA,YAAK+F,MAAL,CAAY,EAACK,MAAMzH,OAAO0K,OAAd,EAAuBrJ,OAAOA,KAA9B,EAAZ;AACD,MAJD,MAIO;AACL,YAAK+F,MAAL,CAAY,EAACK,MAAMzH,OAAO0K,OAAd,EAAZ;AACD;AACF;AACF,EAbD;;AAeA;;;;;;;AAOAjJ,QAAOmB,SAAP,CAAiB0F,OAAjB,GAA2B,UAAUC,MAAV,EAAkB;;AAE3C,QAAKsB,SAAL,GAAiB,KAAjB;AACA,QAAKC,YAAL,GAAoB,IAApB;AACA,UAAO,KAAKjJ,EAAZ;AACA,QAAK0D,IAAL,CAAU,YAAV,EAAwBgE,MAAxB;AACD,EAND;;AAQA;;;;;;;AAOA9G,QAAOmB,SAAP,CAAiB+H,QAAjB,GAA4B,UAAUvD,MAAV,EAAkB;AAC5C,OAAIrG,gBAAgBqG,OAAO1C,GAAP,KAAe,KAAKA,GAAxC;AACA,OAAIkG,qBAAqBxD,OAAOK,IAAP,KAAgBzH,OAAO6K,KAAvB,IAAgCzD,OAAO1C,GAAP,KAAe,GAAxE;;AAEA,OAAI,CAAC3D,aAAD,IAAkB,CAAC6J,kBAAvB,EAA2C;;AAE3C,WAAQxD,OAAOK,IAAf;AACE,UAAKzH,OAAO0K,OAAZ;AACE,YAAKI,SAAL;AACA;;AAEF,UAAK9K,OAAOuK,KAAZ;AACE,YAAKQ,OAAL,CAAa3D,MAAb;AACA;;AAEF,UAAKpH,OAAOsK,YAAZ;AACE,YAAKS,OAAL,CAAa3D,MAAb;AACA;;AAEF,UAAKpH,OAAOgL,GAAZ;AACE,YAAKC,KAAL,CAAW7D,MAAX;AACA;;AAEF,UAAKpH,OAAOkL,UAAZ;AACE,YAAKD,KAAL,CAAW7D,MAAX;AACA;;AAEF,UAAKpH,OAAOmL,UAAZ;AACE,YAAKC,YAAL;AACA;;AAEF,UAAKpL,OAAO6K,KAAZ;AACE,YAAKtG,IAAL,CAAU,OAAV,EAAmB6C,OAAOhB,IAA1B;AACA;AA3BJ;AA6BD,EAnCD;;AAqCA;;;;;;;AAOA3E,QAAOmB,SAAP,CAAiBmI,OAAjB,GAA2B,UAAU3D,MAAV,EAAkB;AAC3C,OAAI8C,OAAO9C,OAAOhB,IAAP,IAAe,EAA1B;;AAGA,OAAI,QAAQgB,OAAOvG,EAAnB,EAAuB;;AAErBqJ,UAAKtD,IAAL,CAAU,KAAKyE,GAAL,CAASjE,OAAOvG,EAAhB,CAAV;AACD;;AAED,OAAI,KAAKgJ,SAAT,EAAoB;AAClBtF,UAAKC,KAAL,CAAW,IAAX,EAAiB0F,IAAjB;AACD,IAFD,MAEO;AACL,UAAKP,aAAL,CAAmB/C,IAAnB,CAAwBsD,IAAxB;AACD;AACF,EAdD;;AAgBA;;;;;;AAMAzI,QAAOmB,SAAP,CAAiByI,GAAjB,GAAuB,UAAUxK,EAAV,EAAc;AACnC,OAAIkF,OAAO,IAAX;AACA,OAAIuF,OAAO,KAAX;AACA,UAAO,YAAY;AACjB;AACA,SAAIA,IAAJ,EAAU;AACVA,YAAO,IAAP;AACA,SAAIpB,OAAOtB,QAAQnE,SAAR,CAAX;;AAGAsB,UAAKqB,MAAL,CAAY;AACVK,aAAMqB,OAAOoB,IAAP,IAAelK,OAAOkL,UAAtB,GAAmClL,OAAOgL,GADtC;AAEVnK,WAAIA,EAFM;AAGVuF,aAAM8D;AAHI,MAAZ;AAKD,IAZD;AAaD,EAhBD;;AAkBA;;;;;;;AAOAzI,QAAOmB,SAAP,CAAiBqI,KAAjB,GAAyB,UAAU7D,MAAV,EAAkB;AACzC,OAAIiE,MAAM,KAAK3B,IAAL,CAAUtC,OAAOvG,EAAjB,CAAV;AACA,OAAI,eAAe,OAAOwK,GAA1B,EAA+B;;AAE7BA,SAAI7G,KAAJ,CAAU,IAAV,EAAgB4C,OAAOhB,IAAvB;AACA,YAAO,KAAKsD,IAAL,CAAUtC,OAAOvG,EAAjB,CAAP;AACD,IAJD,MAIO,CAEN;AACF,EATD;;AAWA;;;;;;AAMAY,QAAOmB,SAAP,CAAiBkI,SAAjB,GAA6B,YAAY;AACvC,QAAKjB,SAAL,GAAiB,IAAjB;AACA,QAAKC,YAAL,GAAoB,KAApB;AACA,QAAKyB,YAAL;AACA,QAAKhH,IAAL,CAAU,SAAV;AACD,EALD;;AAOA;;;;;;AAMA9C,QAAOmB,SAAP,CAAiB2I,YAAjB,GAAgC,YAAY;AAC1C,OAAI3D,CAAJ;AACA,QAAKA,IAAI,CAAT,EAAYA,IAAI,KAAK+B,aAAL,CAAmB3E,MAAnC,EAA2C4C,GAA3C,EAAgD;AAC9CrD,UAAKC,KAAL,CAAW,IAAX,EAAiB,KAAKmF,aAAL,CAAmB/B,CAAnB,CAAjB;AACD;AACD,QAAK+B,aAAL,GAAqB,EAArB;;AAEA,QAAK/B,IAAI,CAAT,EAAYA,IAAI,KAAKgC,UAAL,CAAgB5E,MAAhC,EAAwC4C,GAAxC,EAA6C;AAC3C,UAAKR,MAAL,CAAY,KAAKwC,UAAL,CAAgBhC,CAAhB,CAAZ;AACD;AACD,QAAKgC,UAAL,GAAkB,EAAlB;AACD,EAXD;;AAaA;;;;;;AAMAnI,QAAOmB,SAAP,CAAiBwI,YAAjB,GAAgC,YAAY;;AAE1C,QAAK5E,OAAL;AACA,QAAK8B,OAAL,CAAa,sBAAb;AACD,EAJD;;AAMA;;;;;;;;AAQA7G,QAAOmB,SAAP,CAAiB4D,OAAjB,GAA2B,YAAY;AACrC,OAAI,KAAK1D,IAAT,EAAe;AACb;AACA,UAAK,IAAI8E,IAAI,CAAb,EAAgBA,IAAI,KAAK9E,IAAL,CAAUkC,MAA9B,EAAsC4C,GAAtC,EAA2C;AACzC,YAAK9E,IAAL,CAAU8E,CAAV,EAAapB,OAAb;AACD;AACD,UAAK1D,IAAL,GAAY,IAAZ;AACD;;AAED,QAAK1B,EAAL,CAAQoF,OAAR,CAAgB,IAAhB;AACD,EAVD;;AAYA;;;;;;;AAOA/E,QAAOmB,SAAP,CAAiB+D,KAAjB,GACAlF,OAAOmB,SAAP,CAAiBwF,UAAjB,GAA8B,YAAY;AACxC,OAAI,KAAKyB,SAAT,EAAoB;;AAElB,UAAKzC,MAAL,CAAY,EAAEK,MAAMzH,OAAOmL,UAAf,EAAZ;AACD;;AAED;AACA,QAAK3E,OAAL;;AAEA,OAAI,KAAKqD,SAAT,EAAoB;AAClB;AACA,UAAKvB,OAAL,CAAa,sBAAb;AACD;AACD,UAAO,IAAP;AACD,EAfD;;AAiBA;;;;;;;;AAQA7G,QAAOmB,SAAP,CAAiB4H,QAAjB,GAA4B,UAAUA,QAAV,EAAoB;AAC9C,QAAKT,KAAL,CAAWS,QAAX,GAAsBA,QAAtB;AACA,UAAO,IAAP;AACD,EAHD;;AAKA;;;;;;;;AAQA/I,QAAOmB,SAAP,CAAiByH,MAAjB,GAA0B,UAAUA,MAAV,EAAkB;AAC1C,QAAKN,KAAL,CAAWM,MAAX,GAAoBA,MAApB;AACA,UAAO,IAAP;AACD,EAHD,C;;;;;;AClbA;;AAEA;AACA;;AAEA;;AAEA,6BAA4B,iBAAiB;AAC7C;AACA;;AAEA;AACA;;;;;;;;;ACXA;;;;AAIAlK,QAAOC,OAAP,GAAiBmC,EAAjB;;AAEA;;;;;;;;;AASA,UAASA,EAAT,CAAaX,GAAb,EAAkBwI,EAAlB,EAAsBtE,EAAtB,EAA0B;AACxBlE,OAAIW,EAAJ,CAAO6H,EAAP,EAAWtE,EAAX;AACA,UAAO;AACLU,cAAS,mBAAY;AACnB5E,WAAI4J,cAAJ,CAAmBpB,EAAnB,EAAuBtE,EAAvB;AACD;AAHI,IAAP;AAKD,E;;;;;;ACvBD;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,YAAW,OAAO;AAClB,YAAW,gBAAgB;AAC3B,aAAY;AACZ;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACrBA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAW,OAAO;AAClB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,aAAY;AACZ;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "socket.io.slim.dev.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"io\"] = factory();\n\telse\n\t\troot[\"io\"] = factory();\n})(this, function() {\nreturn \n\n\n// WEBPACK FOOTER //\n// webpack/universalModuleDefinition", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId])\n \t\t\treturn installedModules[moduleId].exports;\n\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\texports: {},\n \t\t\tid: moduleId,\n \t\t\tloaded: false\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.loaded = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(0);\n\n\n\n// WEBPACK FOOTER //\n// webpack/bootstrap 46c68a0009a152ba3192", "\n/**\n * Module dependencies.\n */\n\nvar url = require('./url');\nvar parser = require('socket.io-parser');\nvar Manager = require('./manager');\nvar debug = require('debug')('socket.io-client');\n\n/**\n * Module exports.\n */\n\nmodule.exports = exports = lookup;\n\n/**\n * Managers cache.\n */\n\nvar cache = exports.managers = {};\n\n/**\n * Looks up an existing `Manager` for multiplexing.\n * If the user summons:\n *\n *   `io('http://localhost/a');`\n *   `io('http://localhost/b');`\n *\n * We reuse the existing instance based on same scheme/port/host,\n * and we initialize sockets for each namespace.\n *\n * @api public\n */\n\nfunction lookup (uri, opts) {\n  if (typeof uri === 'object') {\n    opts = uri;\n    uri = undefined;\n  }\n\n  opts = opts || {};\n\n  var parsed = url(uri);\n  var source = parsed.source;\n  var id = parsed.id;\n  var path = parsed.path;\n  var sameNamespace = cache[id] && path in cache[id].nsps;\n  var newConnection = opts.forceNew || opts['force new connection'] ||\n                      false === opts.multiplex || sameNamespace;\n\n  var io;\n\n  if (newConnection) {\n\n    io = Manager(source, opts);\n  } else {\n    if (!cache[id]) {\n\n      cache[id] = Manager(source, opts);\n    }\n    io = cache[id];\n  }\n  if (parsed.query && !opts.query) {\n    opts.query = parsed.query;\n  }\n  return io.socket(parsed.path, opts);\n}\n\n/**\n * Protocol version.\n *\n * @api public\n */\n\nexports.protocol = parser.protocol;\n\n/**\n * `connect`.\n *\n * @param {String} uri\n * @api public\n */\n\nexports.connect = lookup;\n\n/**\n * Expose constructors for standalone build.\n *\n * @api public\n */\n\nexports.Manager = require('./manager');\nexports.Socket = require('./socket');\n\n\n\n// WEBPACK FOOTER //\n// ./lib/index.js", "\n/**\n * Module dependencies.\n */\n\nvar parseuri = require('parseuri');\nvar debug = require('debug')('socket.io-client:url');\n\n/**\n * Module exports.\n */\n\nmodule.exports = url;\n\n/**\n * URL parser.\n *\n * @param {String} url\n * @param {Object} An object meant to mimic window.location.\n *                 Defaults to window.location.\n * @api public\n */\n\nfunction url (uri, loc) {\n  var obj = uri;\n\n  // default to window.location\n  loc = loc || (typeof location !== 'undefined' && location);\n  if (null == uri) uri = loc.protocol + '//' + loc.host;\n\n  // relative path support\n  if ('string' === typeof uri) {\n    if ('/' === uri.charAt(0)) {\n      if ('/' === uri.charAt(1)) {\n        uri = loc.protocol + uri;\n      } else {\n        uri = loc.host + uri;\n      }\n    }\n\n    if (!/^(https?|wss?):\\/\\//.test(uri)) {\n\n      if ('undefined' !== typeof loc) {\n        uri = loc.protocol + '//' + uri;\n      } else {\n        uri = 'https://' + uri;\n      }\n    }\n\n    // parse\n\n    obj = parseuri(uri);\n  }\n\n  // make sure we treat `localhost:80` and `localhost` equally\n  if (!obj.port) {\n    if (/^(http|ws)$/.test(obj.protocol)) {\n      obj.port = '80';\n    } else if (/^(http|ws)s$/.test(obj.protocol)) {\n      obj.port = '443';\n    }\n  }\n\n  obj.path = obj.path || '/';\n\n  var ipv6 = obj.host.indexOf(':') !== -1;\n  var host = ipv6 ? '[' + obj.host + ']' : obj.host;\n\n  // define unique id\n  obj.id = obj.protocol + '://' + host + ':' + obj.port;\n  // define href\n  obj.href = obj.protocol + '://' + host + (loc && loc.port === obj.port ? '' : (':' + obj.port));\n\n  return obj;\n}\n\n\n\n// WEBPACK FOOTER //\n// ./lib/url.js", "/**\n * Parses an URI\n *\n * <AUTHOR> <stevenlevithan.com> (MIT license)\n * @api private\n */\n\nvar re = /^(?:(?![^:@]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\n\nvar parts = [\n    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\n];\n\nmodule.exports = function parseuri(str) {\n    var src = str,\n        b = str.indexOf('['),\n        e = str.indexOf(']');\n\n    if (b != -1 && e != -1) {\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n    }\n\n    var m = re.exec(str || ''),\n        uri = {},\n        i = 14;\n\n    while (i--) {\n        uri[parts[i]] = m[i] || '';\n    }\n\n    if (b != -1 && e != -1) {\n        uri.source = src;\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n        uri.ipv6uri = true;\n    }\n\n    uri.pathNames = pathNames(uri, uri['path']);\n    uri.queryKey = queryKey(uri, uri['query']);\n\n    return uri;\n};\n\nfunction pathNames(obj, path) {\n    var regx = /\\/{2,9}/g,\n        names = path.replace(regx, \"/\").split(\"/\");\n\n    if (path.substr(0, 1) == '/' || path.length === 0) {\n        names.splice(0, 1);\n    }\n    if (path.substr(path.length - 1, 1) == '/') {\n        names.splice(names.length - 1, 1);\n    }\n\n    return names;\n}\n\nfunction queryKey(uri, query) {\n    var data = {};\n\n    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n        if ($1) {\n            data[$1] = $2;\n        }\n    });\n\n    return data;\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/parseuri/index.js\n// module id = 2\n// module chunks = 0", "\nmodule.exports = function () { return function () {}; };\n\n\n\n// WEBPACK FOOTER //\n// ./support/noop.js", "\n/**\n * Module dependencies.\n */\n\nvar debug = require('debug')('socket.io-parser');\nvar Emitter = require('component-emitter');\nvar binary = require('./binary');\nvar isArray = require('isarray');\nvar isBuf = require('./is-buffer');\n\n/**\n * Protocol version.\n *\n * @api public\n */\n\nexports.protocol = 4;\n\n/**\n * Packet types.\n *\n * @api public\n */\n\nexports.types = [\n  'CONNECT',\n  'DISCONNECT',\n  'EVENT',\n  'ACK',\n  'ERROR',\n  'BINARY_EVENT',\n  'BINARY_ACK'\n];\n\n/**\n * Packet type `connect`.\n *\n * @api public\n */\n\nexports.CONNECT = 0;\n\n/**\n * Packet type `disconnect`.\n *\n * @api public\n */\n\nexports.DISCONNECT = 1;\n\n/**\n * Packet type `event`.\n *\n * @api public\n */\n\nexports.EVENT = 2;\n\n/**\n * Packet type `ack`.\n *\n * @api public\n */\n\nexports.ACK = 3;\n\n/**\n * Packet type `error`.\n *\n * @api public\n */\n\nexports.ERROR = 4;\n\n/**\n * Packet type 'binary event'\n *\n * @api public\n */\n\nexports.BINARY_EVENT = 5;\n\n/**\n * Packet type `binary ack`. For acks with binary arguments.\n *\n * @api public\n */\n\nexports.BINARY_ACK = 6;\n\n/**\n * Encoder constructor.\n *\n * @api public\n */\n\nexports.Encoder = Encoder;\n\n/**\n * Decoder constructor.\n *\n * @api public\n */\n\nexports.Decoder = Decoder;\n\n/**\n * A socket.io Encoder instance\n *\n * @api public\n */\n\nfunction Encoder() {}\n\nvar ERROR_PACKET = exports.ERROR + '\"encode error\"';\n\n/**\n * Encode a packet as a single string if non-binary, or as a\n * buffer sequence, depending on packet type.\n *\n * @param {Object} obj - packet object\n * @param {Function} callback - function to handle encodings (likely engine.write)\n * @return Calls callback with Array of encodings\n * @api public\n */\n\nEncoder.prototype.encode = function(obj, callback){\n\n\n  if (exports.BINARY_EVENT === obj.type || exports.BINARY_ACK === obj.type) {\n    encodeAsBinary(obj, callback);\n  } else {\n    var encoding = encodeAsString(obj);\n    callback([encoding]);\n  }\n};\n\n/**\n * Encode packet as string.\n *\n * @param {Object} packet\n * @return {String} encoded\n * @api private\n */\n\nfunction encodeAsString(obj) {\n\n  // first is type\n  var str = '' + obj.type;\n\n  // attachments if we have them\n  if (exports.BINARY_EVENT === obj.type || exports.BINARY_ACK === obj.type) {\n    str += obj.attachments + '-';\n  }\n\n  // if we have a namespace other than `/`\n  // we append it followed by a comma `,`\n  if (obj.nsp && '/' !== obj.nsp) {\n    str += obj.nsp + ',';\n  }\n\n  // immediately followed by the id\n  if (null != obj.id) {\n    str += obj.id;\n  }\n\n  // json data\n  if (null != obj.data) {\n    var payload = tryStringify(obj.data);\n    if (payload !== false) {\n      str += payload;\n    } else {\n      return ERROR_PACKET;\n    }\n  }\n\n\n  return str;\n}\n\nfunction tryStringify(str) {\n  try {\n    return JSON.stringify(str);\n  } catch(e){\n    return false;\n  }\n}\n\n/**\n * Encode packet as 'buffer sequence' by removing blobs, and\n * deconstructing packet into object with placeholders and\n * a list of buffers.\n *\n * @param {Object} packet\n * @return {Buffer} encoded\n * @api private\n */\n\nfunction encodeAsBinary(obj, callback) {\n\n  function writeEncoding(bloblessData) {\n    var deconstruction = binary.deconstructPacket(bloblessData);\n    var pack = encodeAsString(deconstruction.packet);\n    var buffers = deconstruction.buffers;\n\n    buffers.unshift(pack); // add packet info to beginning of data list\n    callback(buffers); // write all the buffers\n  }\n\n  binary.removeBlobs(obj, writeEncoding);\n}\n\n/**\n * A socket.io Decoder instance\n *\n * @return {Object} decoder\n * @api public\n */\n\nfunction Decoder() {\n  this.reconstructor = null;\n}\n\n/**\n * Mix in `Emitter` with Decoder.\n */\n\nEmitter(Decoder.prototype);\n\n/**\n * Decodes an encoded packet string into packet JSON.\n *\n * @param {String} obj - encoded packet\n * @return {Object} packet\n * @api public\n */\n\nDecoder.prototype.add = function(obj) {\n  var packet;\n  if (typeof obj === 'string') {\n    packet = decodeString(obj);\n    if (exports.BINARY_EVENT === packet.type || exports.BINARY_ACK === packet.type) { // binary packet's json\n      this.reconstructor = new BinaryReconstructor(packet);\n\n      // no attachments, labeled binary but no binary data to follow\n      if (this.reconstructor.reconPack.attachments === 0) {\n        this.emit('decoded', packet);\n      }\n    } else { // non-binary full packet\n      this.emit('decoded', packet);\n    }\n  } else if (isBuf(obj) || obj.base64) { // raw binary data\n    if (!this.reconstructor) {\n      throw new Error('got binary data when not reconstructing a packet');\n    } else {\n      packet = this.reconstructor.takeBinaryData(obj);\n      if (packet) { // received final buffer\n        this.reconstructor = null;\n        this.emit('decoded', packet);\n      }\n    }\n  } else {\n    throw new Error('Unknown type: ' + obj);\n  }\n};\n\n/**\n * Decode a packet String (JSON data)\n *\n * @param {String} str\n * @return {Object} packet\n * @api private\n */\n\nfunction decodeString(str) {\n  var i = 0;\n  // look up type\n  var p = {\n    type: Number(str.charAt(0))\n  };\n\n  if (null == exports.types[p.type]) {\n    return error('unknown packet type ' + p.type);\n  }\n\n  // look up attachments if type binary\n  if (exports.BINARY_EVENT === p.type || exports.BINARY_ACK === p.type) {\n    var buf = '';\n    while (str.charAt(++i) !== '-') {\n      buf += str.charAt(i);\n      if (i == str.length) break;\n    }\n    if (buf != Number(buf) || str.charAt(i) !== '-') {\n      throw new Error('Illegal attachments');\n    }\n    p.attachments = Number(buf);\n  }\n\n  // look up namespace (if any)\n  if ('/' === str.charAt(i + 1)) {\n    p.nsp = '';\n    while (++i) {\n      var c = str.charAt(i);\n      if (',' === c) break;\n      p.nsp += c;\n      if (i === str.length) break;\n    }\n  } else {\n    p.nsp = '/';\n  }\n\n  // look up id\n  var next = str.charAt(i + 1);\n  if ('' !== next && Number(next) == next) {\n    p.id = '';\n    while (++i) {\n      var c = str.charAt(i);\n      if (null == c || Number(c) != c) {\n        --i;\n        break;\n      }\n      p.id += str.charAt(i);\n      if (i === str.length) break;\n    }\n    p.id = Number(p.id);\n  }\n\n  // look up json data\n  if (str.charAt(++i)) {\n    var payload = tryParse(str.substr(i));\n    var isPayloadValid = payload !== false && (p.type === exports.ERROR || isArray(payload));\n    if (isPayloadValid) {\n      p.data = payload;\n    } else {\n      return error('invalid payload');\n    }\n  }\n\n\n  return p;\n}\n\nfunction tryParse(str) {\n  try {\n    return JSON.parse(str);\n  } catch(e){\n    return false;\n  }\n}\n\n/**\n * Deallocates a parser's resources\n *\n * @api public\n */\n\nDecoder.prototype.destroy = function() {\n  if (this.reconstructor) {\n    this.reconstructor.finishedReconstruction();\n  }\n};\n\n/**\n * A manager of a binary event's 'buffer sequence'. Should\n * be constructed whenever a packet of type BINARY_EVENT is\n * decoded.\n *\n * @param {Object} packet\n * @return {BinaryReconstructor} initialized reconstructor\n * @api private\n */\n\nfunction BinaryReconstructor(packet) {\n  this.reconPack = packet;\n  this.buffers = [];\n}\n\n/**\n * Method to be called when binary data received from connection\n * after a BINARY_EVENT packet.\n *\n * @param {Buffer | ArrayBuffer} binData - the raw binary data received\n * @return {null | Object} returns null if more binary data is expected or\n *   a reconstructed packet object if all buffers have been received.\n * @api private\n */\n\nBinaryReconstructor.prototype.takeBinaryData = function(binData) {\n  this.buffers.push(binData);\n  if (this.buffers.length === this.reconPack.attachments) { // done with buffer list\n    var packet = binary.reconstructPacket(this.reconPack, this.buffers);\n    this.finishedReconstruction();\n    return packet;\n  }\n  return null;\n};\n\n/**\n * Cleans up binary packet reconstruction variables.\n *\n * @api private\n */\n\nBinaryReconstructor.prototype.finishedReconstruction = function() {\n  this.reconPack = null;\n  this.buffers = [];\n};\n\nfunction error(msg) {\n  return {\n    type: exports.ERROR,\n    data: 'parser error: ' + msg\n  };\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/socket.io-parser/index.js\n// module id = 4\n// module chunks = 0", "\r\n/**\r\n * Expose `Emitter`.\r\n */\r\n\r\nif (typeof module !== 'undefined') {\r\n  module.exports = Emitter;\r\n}\r\n\r\n/**\r\n * Initialize a new `Emitter`.\r\n *\r\n * @api public\r\n */\r\n\r\nfunction Emitter(obj) {\r\n  if (obj) return mixin(obj);\r\n};\r\n\r\n/**\r\n * Mixin the emitter properties.\r\n *\r\n * @param {Object} obj\r\n * @return {Object}\r\n * @api private\r\n */\r\n\r\nfunction mixin(obj) {\r\n  for (var key in Emitter.prototype) {\r\n    obj[key] = Emitter.prototype[key];\r\n  }\r\n  return obj;\r\n}\r\n\r\n/**\r\n * Listen on the given `event` with `fn`.\r\n *\r\n * @param {String} event\r\n * @param {Function} fn\r\n * @return {Emitter}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.on =\r\nEmitter.prototype.addEventListener = function(event, fn){\r\n  this._callbacks = this._callbacks || {};\r\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\r\n    .push(fn);\r\n  return this;\r\n};\r\n\r\n/**\r\n * Adds an `event` listener that will be invoked a single\r\n * time then automatically removed.\r\n *\r\n * @param {String} event\r\n * @param {Function} fn\r\n * @return {Emitter}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.once = function(event, fn){\r\n  function on() {\r\n    this.off(event, on);\r\n    fn.apply(this, arguments);\r\n  }\r\n\r\n  on.fn = fn;\r\n  this.on(event, on);\r\n  return this;\r\n};\r\n\r\n/**\r\n * Remove the given callback for `event` or all\r\n * registered callbacks.\r\n *\r\n * @param {String} event\r\n * @param {Function} fn\r\n * @return {Emitter}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.off =\r\nEmitter.prototype.removeListener =\r\nEmitter.prototype.removeAllListeners =\r\nEmitter.prototype.removeEventListener = function(event, fn){\r\n  this._callbacks = this._callbacks || {};\r\n\r\n  // all\r\n  if (0 == arguments.length) {\r\n    this._callbacks = {};\r\n    return this;\r\n  }\r\n\r\n  // specific event\r\n  var callbacks = this._callbacks['$' + event];\r\n  if (!callbacks) return this;\r\n\r\n  // remove all handlers\r\n  if (1 == arguments.length) {\r\n    delete this._callbacks['$' + event];\r\n    return this;\r\n  }\r\n\r\n  // remove specific handler\r\n  var cb;\r\n  for (var i = 0; i < callbacks.length; i++) {\r\n    cb = callbacks[i];\r\n    if (cb === fn || cb.fn === fn) {\r\n      callbacks.splice(i, 1);\r\n      break;\r\n    }\r\n  }\r\n\r\n  // Remove event specific arrays for event types that no\r\n  // one is subscribed for to avoid memory leak.\r\n  if (callbacks.length === 0) {\r\n    delete this._callbacks['$' + event];\r\n  }\r\n\r\n  return this;\r\n};\r\n\r\n/**\r\n * Emit `event` with the given args.\r\n *\r\n * @param {String} event\r\n * @param {Mixed} ...\r\n * @return {Emitter}\r\n */\r\n\r\nEmitter.prototype.emit = function(event){\r\n  this._callbacks = this._callbacks || {};\r\n\r\n  var args = new Array(arguments.length - 1)\r\n    , callbacks = this._callbacks['$' + event];\r\n\r\n  for (var i = 1; i < arguments.length; i++) {\r\n    args[i - 1] = arguments[i];\r\n  }\r\n\r\n  if (callbacks) {\r\n    callbacks = callbacks.slice(0);\r\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\r\n      callbacks[i].apply(this, args);\r\n    }\r\n  }\r\n\r\n  return this;\r\n};\r\n\r\n/**\r\n * Return array of callbacks for `event`.\r\n *\r\n * @param {String} event\r\n * @return {Array}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.listeners = function(event){\r\n  this._callbacks = this._callbacks || {};\r\n  return this._callbacks['$' + event] || [];\r\n};\r\n\r\n/**\r\n * Check if this emitter has `event` handlers.\r\n *\r\n * @param {String} event\r\n * @return {Boolean}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.hasListeners = function(event){\r\n  return !! this.listeners(event).length;\r\n};\r\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/component-emitter/index.js\n// module id = 5\n// module chunks = 0", "/*global Blob,File*/\n\n/**\n * Module requirements\n */\n\nvar isArray = require('isarray');\nvar isBuf = require('./is-buffer');\nvar toString = Object.prototype.toString;\nvar withNativeBlob = typeof Blob === 'function' || (typeof Blob !== 'undefined' && toString.call(Blob) === '[object BlobConstructor]');\nvar withNativeFile = typeof File === 'function' || (typeof File !== 'undefined' && toString.call(File) === '[object FileConstructor]');\n\n/**\n * Replaces every Buffer | ArrayBuffer in packet with a numbered placeholder.\n * Anything with blobs or files should be fed through removeBlobs before coming\n * here.\n *\n * @param {Object} packet - socket.io event packet\n * @return {Object} with deconstructed packet and list of buffers\n * @api public\n */\n\nexports.deconstructPacket = function(packet) {\n  var buffers = [];\n  var packetData = packet.data;\n  var pack = packet;\n  pack.data = _deconstructPacket(packetData, buffers);\n  pack.attachments = buffers.length; // number of binary 'attachments'\n  return {packet: pack, buffers: buffers};\n};\n\nfunction _deconstructPacket(data, buffers) {\n  if (!data) return data;\n\n  if (isBuf(data)) {\n    var placeholder = { _placeholder: true, num: buffers.length };\n    buffers.push(data);\n    return placeholder;\n  } else if (isArray(data)) {\n    var newData = new Array(data.length);\n    for (var i = 0; i < data.length; i++) {\n      newData[i] = _deconstructPacket(data[i], buffers);\n    }\n    return newData;\n  } else if (typeof data === 'object' && !(data instanceof Date)) {\n    var newData = {};\n    for (var key in data) {\n      newData[key] = _deconstructPacket(data[key], buffers);\n    }\n    return newData;\n  }\n  return data;\n}\n\n/**\n * Reconstructs a binary packet from its placeholder packet and buffers\n *\n * @param {Object} packet - event packet with placeholders\n * @param {Array} buffers - binary buffers to put in placeholder positions\n * @return {Object} reconstructed packet\n * @api public\n */\n\nexports.reconstructPacket = function(packet, buffers) {\n  packet.data = _reconstructPacket(packet.data, buffers);\n  packet.attachments = undefined; // no longer useful\n  return packet;\n};\n\nfunction _reconstructPacket(data, buffers) {\n  if (!data) return data;\n\n  if (data && data._placeholder) {\n    return buffers[data.num]; // appropriate buffer (should be natural order anyway)\n  } else if (isArray(data)) {\n    for (var i = 0; i < data.length; i++) {\n      data[i] = _reconstructPacket(data[i], buffers);\n    }\n  } else if (typeof data === 'object') {\n    for (var key in data) {\n      data[key] = _reconstructPacket(data[key], buffers);\n    }\n  }\n\n  return data;\n}\n\n/**\n * Asynchronously removes Blobs or Files from data via\n * FileReader's readAsArrayBuffer method. Used before encoding\n * data as msgpack. Calls callback with the blobless data.\n *\n * @param {Object} data\n * @param {Function} callback\n * @api private\n */\n\nexports.removeBlobs = function(data, callback) {\n  function _removeBlobs(obj, curKey, containingObject) {\n    if (!obj) return obj;\n\n    // convert any blob\n    if ((withNativeBlob && obj instanceof Blob) ||\n        (withNativeFile && obj instanceof File)) {\n      pendingBlobs++;\n\n      // async filereader\n      var fileReader = new FileReader();\n      fileReader.onload = function() { // this.result == arraybuffer\n        if (containingObject) {\n          containingObject[curKey] = this.result;\n        }\n        else {\n          bloblessData = this.result;\n        }\n\n        // if nothing pending its callback time\n        if(! --pendingBlobs) {\n          callback(bloblessData);\n        }\n      };\n\n      fileReader.readAsArrayBuffer(obj); // blob -> arraybuffer\n    } else if (isArray(obj)) { // handle array\n      for (var i = 0; i < obj.length; i++) {\n        _removeBlobs(obj[i], i, obj);\n      }\n    } else if (typeof obj === 'object' && !isBuf(obj)) { // and object\n      for (var key in obj) {\n        _removeBlobs(obj[key], key, obj);\n      }\n    }\n  }\n\n  var pendingBlobs = 0;\n  var bloblessData = data;\n  _removeBlobs(bloblessData);\n  if (!pendingBlobs) {\n    callback(bloblessData);\n  }\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/socket.io-parser/binary.js\n// module id = 6\n// module chunks = 0", "var toString = {}.toString;\n\nmodule.exports = Array.isArray || function (arr) {\n  return toString.call(arr) == '[object Array]';\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/isarray/index.js\n// module id = 7\n// module chunks = 0", "\nmodule.exports = isBuf;\n\nvar withNativeBuffer = typeof Buffer === 'function' && typeof Buffer.isBuffer === 'function';\nvar withNativeArrayBuffer = typeof ArrayBuffer === 'function';\n\nvar isView = function (obj) {\n  return typeof ArrayBuffer.isView === 'function' ? ArrayBuffer.isView(obj) : (obj.buffer instanceof ArrayBuffer);\n};\n\n/**\n * Returns true if obj is a buffer or an arraybuffer.\n *\n * @api private\n */\n\nfunction isBuf(obj) {\n  return (withNativeBuffer && Buffer.isBuffer(obj)) ||\n          (withNativeArrayBuffer && (obj instanceof ArrayBuffer || isView(obj)));\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/socket.io-parser/is-buffer.js\n// module id = 8\n// module chunks = 0", "\n/**\n * Module dependencies.\n */\n\nvar eio = require('engine.io-client');\nvar Socket = require('./socket');\nvar Emitter = require('component-emitter');\nvar parser = require('socket.io-parser');\nvar on = require('./on');\nvar bind = require('component-bind');\nvar debug = require('debug')('socket.io-client:manager');\nvar indexOf = require('indexof');\nvar Backoff = require('backo2');\n\n/**\n * IE6+ hasOwnProperty\n */\n\nvar has = Object.prototype.hasOwnProperty;\n\n/**\n * Module exports\n */\n\nmodule.exports = Manager;\n\n/**\n * `Manager` constructor.\n *\n * @param {String} engine instance or engine uri/opts\n * @param {Object} options\n * @api public\n */\n\nfunction Manager (uri, opts) {\n  if (!(this instanceof Manager)) return new Manager(uri, opts);\n  if (uri && ('object' === typeof uri)) {\n    opts = uri;\n    uri = undefined;\n  }\n  opts = opts || {};\n\n  opts.path = opts.path || '/socket.io';\n  this.nsps = {};\n  this.subs = [];\n  this.opts = opts;\n  this.reconnection(opts.reconnection !== false);\n  this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n  this.reconnectionDelay(opts.reconnectionDelay || 1000);\n  this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n  this.randomizationFactor(opts.randomizationFactor || 0.5);\n  this.backoff = new Backoff({\n    min: this.reconnectionDelay(),\n    max: this.reconnectionDelayMax(),\n    jitter: this.randomizationFactor()\n  });\n  this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n  this.readyState = 'closed';\n  this.uri = uri;\n  this.connecting = [];\n  this.lastPing = null;\n  this.encoding = false;\n  this.packetBuffer = [];\n  var _parser = opts.parser || parser;\n  this.encoder = new _parser.Encoder();\n  this.decoder = new _parser.Decoder();\n  this.autoConnect = opts.autoConnect !== false;\n  if (this.autoConnect) this.open();\n}\n\n/**\n * Propagate given event to sockets and emit on `this`\n *\n * @api private\n */\n\nManager.prototype.emitAll = function () {\n  this.emit.apply(this, arguments);\n  for (var nsp in this.nsps) {\n    if (has.call(this.nsps, nsp)) {\n      this.nsps[nsp].emit.apply(this.nsps[nsp], arguments);\n    }\n  }\n};\n\n/**\n * Update `socket.id` of all sockets\n *\n * @api private\n */\n\nManager.prototype.updateSocketIds = function () {\n  for (var nsp in this.nsps) {\n    if (has.call(this.nsps, nsp)) {\n      this.nsps[nsp].id = this.generateId(nsp);\n    }\n  }\n};\n\n/**\n * generate `socket.id` for the given `nsp`\n *\n * @param {String} nsp\n * @return {String}\n * @api private\n */\n\nManager.prototype.generateId = function (nsp) {\n  return (nsp === '/' ? '' : (nsp + '#')) + this.engine.id;\n};\n\n/**\n * Mix in `Emitter`.\n */\n\nEmitter(Manager.prototype);\n\n/**\n * Sets the `reconnection` config.\n *\n * @param {Boolean} true/false if it should automatically reconnect\n * @return {Manager} self or value\n * @api public\n */\n\nManager.prototype.reconnection = function (v) {\n  if (!arguments.length) return this._reconnection;\n  this._reconnection = !!v;\n  return this;\n};\n\n/**\n * Sets the reconnection attempts config.\n *\n * @param {Number} max reconnection attempts before giving up\n * @return {Manager} self or value\n * @api public\n */\n\nManager.prototype.reconnectionAttempts = function (v) {\n  if (!arguments.length) return this._reconnectionAttempts;\n  this._reconnectionAttempts = v;\n  return this;\n};\n\n/**\n * Sets the delay between reconnections.\n *\n * @param {Number} delay\n * @return {Manager} self or value\n * @api public\n */\n\nManager.prototype.reconnectionDelay = function (v) {\n  if (!arguments.length) return this._reconnectionDelay;\n  this._reconnectionDelay = v;\n  this.backoff && this.backoff.setMin(v);\n  return this;\n};\n\nManager.prototype.randomizationFactor = function (v) {\n  if (!arguments.length) return this._randomizationFactor;\n  this._randomizationFactor = v;\n  this.backoff && this.backoff.setJitter(v);\n  return this;\n};\n\n/**\n * Sets the maximum delay between reconnections.\n *\n * @param {Number} delay\n * @return {Manager} self or value\n * @api public\n */\n\nManager.prototype.reconnectionDelayMax = function (v) {\n  if (!arguments.length) return this._reconnectionDelayMax;\n  this._reconnectionDelayMax = v;\n  this.backoff && this.backoff.setMax(v);\n  return this;\n};\n\n/**\n * Sets the connection timeout. `false` to disable\n *\n * @return {Manager} self or value\n * @api public\n */\n\nManager.prototype.timeout = function (v) {\n  if (!arguments.length) return this._timeout;\n  this._timeout = v;\n  return this;\n};\n\n/**\n * Starts trying to reconnect if reconnection is enabled and we have not\n * started reconnecting yet\n *\n * @api private\n */\n\nManager.prototype.maybeReconnectOnOpen = function () {\n  // Only try to reconnect if it's the first time we're connecting\n  if (!this.reconnecting && this._reconnection && this.backoff.attempts === 0) {\n    // keeps reconnection from firing twice for the same reconnection loop\n    this.reconnect();\n  }\n};\n\n/**\n * Sets the current transport `socket`.\n *\n * @param {Function} optional, callback\n * @return {Manager} self\n * @api public\n */\n\nManager.prototype.open =\nManager.prototype.connect = function (fn, opts) {\n\n  if (~this.readyState.indexOf('open')) return this;\n\n\n  this.engine = eio(this.uri, this.opts);\n  var socket = this.engine;\n  var self = this;\n  this.readyState = 'opening';\n  this.skipReconnect = false;\n\n  // emit `open`\n  var openSub = on(socket, 'open', function () {\n    self.onopen();\n    fn && fn();\n  });\n\n  // emit `connect_error`\n  var errorSub = on(socket, 'error', function (data) {\n\n    self.cleanup();\n    self.readyState = 'closed';\n    self.emitAll('connect_error', data);\n    if (fn) {\n      var err = new Error('Connection error');\n      err.data = data;\n      fn(err);\n    } else {\n      // Only do this if there is no fn to handle the error\n      self.maybeReconnectOnOpen();\n    }\n  });\n\n  // emit `connect_timeout`\n  if (false !== this._timeout) {\n    var timeout = this._timeout;\n\n\n    if (timeout === 0) {\n      openSub.destroy(); // prevents a race condition with the 'open' event\n    }\n\n    // set timer\n    var timer = setTimeout(function () {\n\n      openSub.destroy();\n      socket.close();\n      socket.emit('error', 'timeout');\n      self.emitAll('connect_timeout', timeout);\n    }, timeout);\n\n    this.subs.push({\n      destroy: function () {\n        clearTimeout(timer);\n      }\n    });\n  }\n\n  this.subs.push(openSub);\n  this.subs.push(errorSub);\n\n  return this;\n};\n\n/**\n * Called upon transport open.\n *\n * @api private\n */\n\nManager.prototype.onopen = function () {\n\n\n  // clear old subs\n  this.cleanup();\n\n  // mark as open\n  this.readyState = 'open';\n  this.emit('open');\n\n  // add new subs\n  var socket = this.engine;\n  this.subs.push(on(socket, 'data', bind(this, 'ondata')));\n  this.subs.push(on(socket, 'ping', bind(this, 'onping')));\n  this.subs.push(on(socket, 'pong', bind(this, 'onpong')));\n  this.subs.push(on(socket, 'error', bind(this, 'onerror')));\n  this.subs.push(on(socket, 'close', bind(this, 'onclose')));\n  this.subs.push(on(this.decoder, 'decoded', bind(this, 'ondecoded')));\n};\n\n/**\n * Called upon a ping.\n *\n * @api private\n */\n\nManager.prototype.onping = function () {\n  this.lastPing = new Date();\n  this.emitAll('ping');\n};\n\n/**\n * Called upon a packet.\n *\n * @api private\n */\n\nManager.prototype.onpong = function () {\n  this.emitAll('pong', new Date() - this.lastPing);\n};\n\n/**\n * Called with data.\n *\n * @api private\n */\n\nManager.prototype.ondata = function (data) {\n  this.decoder.add(data);\n};\n\n/**\n * Called when parser fully decodes a packet.\n *\n * @api private\n */\n\nManager.prototype.ondecoded = function (packet) {\n  this.emit('packet', packet);\n};\n\n/**\n * Called upon socket error.\n *\n * @api private\n */\n\nManager.prototype.onerror = function (err) {\n\n  this.emitAll('error', err);\n};\n\n/**\n * Creates a new socket for the given `nsp`.\n *\n * @return {Socket}\n * @api public\n */\n\nManager.prototype.socket = function (nsp, opts) {\n  var socket = this.nsps[nsp];\n  if (!socket) {\n    socket = new Socket(this, nsp, opts);\n    this.nsps[nsp] = socket;\n    var self = this;\n    socket.on('connecting', onConnecting);\n    socket.on('connect', function () {\n      socket.id = self.generateId(nsp);\n    });\n\n    if (this.autoConnect) {\n      // manually call here since connecting event is fired before listening\n      onConnecting();\n    }\n  }\n\n  function onConnecting () {\n    if (!~indexOf(self.connecting, socket)) {\n      self.connecting.push(socket);\n    }\n  }\n\n  return socket;\n};\n\n/**\n * Called upon a socket close.\n *\n * @param {Socket} socket\n */\n\nManager.prototype.destroy = function (socket) {\n  var index = indexOf(this.connecting, socket);\n  if (~index) this.connecting.splice(index, 1);\n  if (this.connecting.length) return;\n\n  this.close();\n};\n\n/**\n * Writes a packet.\n *\n * @param {Object} packet\n * @api private\n */\n\nManager.prototype.packet = function (packet) {\n\n  var self = this;\n  if (packet.query && packet.type === 0) packet.nsp += '?' + packet.query;\n\n  if (!self.encoding) {\n    // encode, then write to engine with result\n    self.encoding = true;\n    this.encoder.encode(packet, function (encodedPackets) {\n      for (var i = 0; i < encodedPackets.length; i++) {\n        self.engine.write(encodedPackets[i], packet.options);\n      }\n      self.encoding = false;\n      self.processPacketQueue();\n    });\n  } else { // add packet to the queue\n    self.packetBuffer.push(packet);\n  }\n};\n\n/**\n * If packet buffer is non-empty, begins encoding the\n * next packet in line.\n *\n * @api private\n */\n\nManager.prototype.processPacketQueue = function () {\n  if (this.packetBuffer.length > 0 && !this.encoding) {\n    var pack = this.packetBuffer.shift();\n    this.packet(pack);\n  }\n};\n\n/**\n * Clean up transport subscriptions and packet buffer.\n *\n * @api private\n */\n\nManager.prototype.cleanup = function () {\n\n\n  var subsLength = this.subs.length;\n  for (var i = 0; i < subsLength; i++) {\n    var sub = this.subs.shift();\n    sub.destroy();\n  }\n\n  this.packetBuffer = [];\n  this.encoding = false;\n  this.lastPing = null;\n\n  this.decoder.destroy();\n};\n\n/**\n * Close the current socket.\n *\n * @api private\n */\n\nManager.prototype.close =\nManager.prototype.disconnect = function () {\n\n  this.skipReconnect = true;\n  this.reconnecting = false;\n  if ('opening' === this.readyState) {\n    // `onclose` will not fire because\n    // an open event never happened\n    this.cleanup();\n  }\n  this.backoff.reset();\n  this.readyState = 'closed';\n  if (this.engine) this.engine.close();\n};\n\n/**\n * Called upon engine close.\n *\n * @api private\n */\n\nManager.prototype.onclose = function (reason) {\n\n\n  this.cleanup();\n  this.backoff.reset();\n  this.readyState = 'closed';\n  this.emit('close', reason);\n\n  if (this._reconnection && !this.skipReconnect) {\n    this.reconnect();\n  }\n};\n\n/**\n * Attempt a reconnection.\n *\n * @api private\n */\n\nManager.prototype.reconnect = function () {\n  if (this.reconnecting || this.skipReconnect) return this;\n\n  var self = this;\n\n  if (this.backoff.attempts >= this._reconnectionAttempts) {\n\n    this.backoff.reset();\n    this.emitAll('reconnect_failed');\n    this.reconnecting = false;\n  } else {\n    var delay = this.backoff.duration();\n\n\n    this.reconnecting = true;\n    var timer = setTimeout(function () {\n      if (self.skipReconnect) return;\n\n\n      self.emitAll('reconnect_attempt', self.backoff.attempts);\n      self.emitAll('reconnecting', self.backoff.attempts);\n\n      // check again for the case socket closed in above events\n      if (self.skipReconnect) return;\n\n      self.open(function (err) {\n        if (err) {\n\n          self.reconnecting = false;\n          self.reconnect();\n          self.emitAll('reconnect_error', err.data);\n        } else {\n\n          self.onreconnect();\n        }\n      });\n    }, delay);\n\n    this.subs.push({\n      destroy: function () {\n        clearTimeout(timer);\n      }\n    });\n  }\n};\n\n/**\n * Called upon successful reconnect.\n *\n * @api private\n */\n\nManager.prototype.onreconnect = function () {\n  var attempt = this.backoff.attempts;\n  this.reconnecting = false;\n  this.backoff.reset();\n  this.updateSocketIds();\n  this.emitAll('reconnect', attempt);\n};\n\n\n\n// WEBPACK FOOTER //\n// ./lib/manager.js", "\nmodule.exports = require('./socket');\n\n/**\n * Exports parser\n *\n * @api public\n *\n */\nmodule.exports.parser = require('engine.io-parser');\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/index.js\n// module id = 10\n// module chunks = 0", "/**\n * Module dependencies.\n */\n\nvar transports = require('./transports/index');\nvar Emitter = require('component-emitter');\nvar debug = require('debug')('engine.io-client:socket');\nvar index = require('indexof');\nvar parser = require('engine.io-parser');\nvar parseuri = require('parseuri');\nvar parseqs = require('parseqs');\n\n/**\n * Module exports.\n */\n\nmodule.exports = Socket;\n\n/**\n * Socket constructor.\n *\n * @param {String|Object} uri or options\n * @param {Object} options\n * @api public\n */\n\nfunction Socket (uri, opts) {\n  if (!(this instanceof Socket)) return new Socket(uri, opts);\n\n  opts = opts || {};\n\n  if (uri && 'object' === typeof uri) {\n    opts = uri;\n    uri = null;\n  }\n\n  if (uri) {\n    uri = parseuri(uri);\n    opts.hostname = uri.host;\n    opts.secure = uri.protocol === 'https' || uri.protocol === 'wss';\n    opts.port = uri.port;\n    if (uri.query) opts.query = uri.query;\n  } else if (opts.host) {\n    opts.hostname = parseuri(opts.host).host;\n  }\n\n  this.secure = null != opts.secure ? opts.secure\n    : (typeof location !== 'undefined' && 'https:' === location.protocol);\n\n  if (opts.hostname && !opts.port) {\n    // if no port is specified manually, use the protocol default\n    opts.port = this.secure ? '443' : '80';\n  }\n\n  this.agent = opts.agent || false;\n  this.hostname = opts.hostname ||\n    (typeof location !== 'undefined' ? location.hostname : 'localhost');\n  this.port = opts.port || (typeof location !== 'undefined' && location.port\n      ? location.port\n      : (this.secure ? 443 : 80));\n  this.query = opts.query || {};\n  if ('string' === typeof this.query) this.query = parseqs.decode(this.query);\n  this.upgrade = false !== opts.upgrade;\n  this.path = (opts.path || '/engine.io').replace(/\\/$/, '') + '/';\n  this.forceJSONP = !!opts.forceJSONP;\n  this.jsonp = false !== opts.jsonp;\n  this.forceBase64 = !!opts.forceBase64;\n  this.enablesXDR = !!opts.enablesXDR;\n  this.withCredentials = false !== opts.withCredentials;\n  this.timestampParam = opts.timestampParam || 't';\n  this.timestampRequests = opts.timestampRequests;\n  this.transports = opts.transports || ['polling', 'websocket'];\n  this.transportOptions = opts.transportOptions || {};\n  this.readyState = '';\n  this.writeBuffer = [];\n  this.prevBufferLen = 0;\n  this.policyPort = opts.policyPort || 843;\n  this.rememberUpgrade = opts.rememberUpgrade || false;\n  this.binaryType = null;\n  this.onlyBinaryUpgrades = opts.onlyBinaryUpgrades;\n  this.perMessageDeflate = false !== opts.perMessageDeflate ? (opts.perMessageDeflate || {}) : false;\n\n  if (true === this.perMessageDeflate) this.perMessageDeflate = {};\n  if (this.perMessageDeflate && null == this.perMessageDeflate.threshold) {\n    this.perMessageDeflate.threshold = 1024;\n  }\n\n  // SSL options for Node.js client\n  this.pfx = opts.pfx || null;\n  this.key = opts.key || null;\n  this.passphrase = opts.passphrase || null;\n  this.cert = opts.cert || null;\n  this.ca = opts.ca || null;\n  this.ciphers = opts.ciphers || null;\n  this.rejectUnauthorized = opts.rejectUnauthorized === undefined ? true : opts.rejectUnauthorized;\n  this.forceNode = !!opts.forceNode;\n\n  // detect ReactNative environment\n  this.isReactNative = (typeof navigator !== 'undefined' && typeof navigator.product === 'string' && navigator.product.toLowerCase() === 'reactnative');\n\n  // other options for Node.js or ReactNative client\n  if (typeof self === 'undefined' || this.isReactNative) {\n    if (opts.extraHeaders && Object.keys(opts.extraHeaders).length > 0) {\n      this.extraHeaders = opts.extraHeaders;\n    }\n\n    if (opts.localAddress) {\n      this.localAddress = opts.localAddress;\n    }\n  }\n\n  // set on handshake\n  this.id = null;\n  this.upgrades = null;\n  this.pingInterval = null;\n  this.pingTimeout = null;\n\n  // set on heartbeat\n  this.pingIntervalTimer = null;\n  this.pingTimeoutTimer = null;\n\n  this.open();\n}\n\nSocket.priorWebsocketSuccess = false;\n\n/**\n * Mix in `Emitter`.\n */\n\nEmitter(Socket.prototype);\n\n/**\n * Protocol version.\n *\n * @api public\n */\n\nSocket.protocol = parser.protocol; // this is an int\n\n/**\n * Expose deps for legacy compatibility\n * and standalone browser access.\n */\n\nSocket.Socket = Socket;\nSocket.Transport = require('./transport');\nSocket.transports = require('./transports/index');\nSocket.parser = require('engine.io-parser');\n\n/**\n * Creates transport of the given type.\n *\n * @param {String} transport name\n * @return {Transport}\n * @api private\n */\n\nSocket.prototype.createTransport = function (name) {\n\n  var query = clone(this.query);\n\n  // append engine.io protocol identifier\n  query.EIO = parser.protocol;\n\n  // transport name\n  query.transport = name;\n\n  // per-transport options\n  var options = this.transportOptions[name] || {};\n\n  // session id if we already have one\n  if (this.id) query.sid = this.id;\n\n  var transport = new transports[name]({\n    query: query,\n    socket: this,\n    agent: options.agent || this.agent,\n    hostname: options.hostname || this.hostname,\n    port: options.port || this.port,\n    secure: options.secure || this.secure,\n    path: options.path || this.path,\n    forceJSONP: options.forceJSONP || this.forceJSONP,\n    jsonp: options.jsonp || this.jsonp,\n    forceBase64: options.forceBase64 || this.forceBase64,\n    enablesXDR: options.enablesXDR || this.enablesXDR,\n    withCredentials: options.withCredentials || this.withCredentials,\n    timestampRequests: options.timestampRequests || this.timestampRequests,\n    timestampParam: options.timestampParam || this.timestampParam,\n    policyPort: options.policyPort || this.policyPort,\n    pfx: options.pfx || this.pfx,\n    key: options.key || this.key,\n    passphrase: options.passphrase || this.passphrase,\n    cert: options.cert || this.cert,\n    ca: options.ca || this.ca,\n    ciphers: options.ciphers || this.ciphers,\n    rejectUnauthorized: options.rejectUnauthorized || this.rejectUnauthorized,\n    perMessageDeflate: options.perMessageDeflate || this.perMessageDeflate,\n    extraHeaders: options.extraHeaders || this.extraHeaders,\n    forceNode: options.forceNode || this.forceNode,\n    localAddress: options.localAddress || this.localAddress,\n    requestTimeout: options.requestTimeout || this.requestTimeout,\n    protocols: options.protocols || void (0),\n    isReactNative: this.isReactNative\n  });\n\n  return transport;\n};\n\nfunction clone (obj) {\n  var o = {};\n  for (var i in obj) {\n    if (obj.hasOwnProperty(i)) {\n      o[i] = obj[i];\n    }\n  }\n  return o;\n}\n\n/**\n * Initializes transport to use and starts probe.\n *\n * @api private\n */\nSocket.prototype.open = function () {\n  var transport;\n  if (this.rememberUpgrade && Socket.priorWebsocketSuccess && this.transports.indexOf('websocket') !== -1) {\n    transport = 'websocket';\n  } else if (0 === this.transports.length) {\n    // Emit error on next tick so it can be listened to\n    var self = this;\n    setTimeout(function () {\n      self.emit('error', 'No transports available');\n    }, 0);\n    return;\n  } else {\n    transport = this.transports[0];\n  }\n  this.readyState = 'opening';\n\n  // Retry with the next transport if the transport is disabled (jsonp: false)\n  try {\n    transport = this.createTransport(transport);\n  } catch (e) {\n    this.transports.shift();\n    this.open();\n    return;\n  }\n\n  transport.open();\n  this.setTransport(transport);\n};\n\n/**\n * Sets the current transport. Disables the existing one (if any).\n *\n * @api private\n */\n\nSocket.prototype.setTransport = function (transport) {\n\n  var self = this;\n\n  if (this.transport) {\n\n    this.transport.removeAllListeners();\n  }\n\n  // set up transport\n  this.transport = transport;\n\n  // set up transport listeners\n  transport\n  .on('drain', function () {\n    self.onDrain();\n  })\n  .on('packet', function (packet) {\n    self.onPacket(packet);\n  })\n  .on('error', function (e) {\n    self.onError(e);\n  })\n  .on('close', function () {\n    self.onClose('transport close');\n  });\n};\n\n/**\n * Probes a transport.\n *\n * @param {String} transport name\n * @api private\n */\n\nSocket.prototype.probe = function (name) {\n\n  var transport = this.createTransport(name, { probe: 1 });\n  var failed = false;\n  var self = this;\n\n  Socket.priorWebsocketSuccess = false;\n\n  function onTransportOpen () {\n    if (self.onlyBinaryUpgrades) {\n      var upgradeLosesBinary = !this.supportsBinary && self.transport.supportsBinary;\n      failed = failed || upgradeLosesBinary;\n    }\n    if (failed) return;\n\n\n    transport.send([{ type: 'ping', data: 'probe' }]);\n    transport.once('packet', function (msg) {\n      if (failed) return;\n      if ('pong' === msg.type && 'probe' === msg.data) {\n\n        self.upgrading = true;\n        self.emit('upgrading', transport);\n        if (!transport) return;\n        Socket.priorWebsocketSuccess = 'websocket' === transport.name;\n\n\n        self.transport.pause(function () {\n          if (failed) return;\n          if ('closed' === self.readyState) return;\n\n\n          cleanup();\n\n          self.setTransport(transport);\n          transport.send([{ type: 'upgrade' }]);\n          self.emit('upgrade', transport);\n          transport = null;\n          self.upgrading = false;\n          self.flush();\n        });\n      } else {\n\n        var err = new Error('probe error');\n        err.transport = transport.name;\n        self.emit('upgradeError', err);\n      }\n    });\n  }\n\n  function freezeTransport () {\n    if (failed) return;\n\n    // Any callback called by transport should be ignored since now\n    failed = true;\n\n    cleanup();\n\n    transport.close();\n    transport = null;\n  }\n\n  // Handle any error that happens while probing\n  function onerror (err) {\n    var error = new Error('probe error: ' + err);\n    error.transport = transport.name;\n\n    freezeTransport();\n\n\n\n    self.emit('upgradeError', error);\n  }\n\n  function onTransportClose () {\n    onerror('transport closed');\n  }\n\n  // When the socket is closed while we're probing\n  function onclose () {\n    onerror('socket closed');\n  }\n\n  // When the socket is upgraded while we're probing\n  function onupgrade (to) {\n    if (transport && to.name !== transport.name) {\n\n      freezeTransport();\n    }\n  }\n\n  // Remove all listeners on the transport and on self\n  function cleanup () {\n    transport.removeListener('open', onTransportOpen);\n    transport.removeListener('error', onerror);\n    transport.removeListener('close', onTransportClose);\n    self.removeListener('close', onclose);\n    self.removeListener('upgrading', onupgrade);\n  }\n\n  transport.once('open', onTransportOpen);\n  transport.once('error', onerror);\n  transport.once('close', onTransportClose);\n\n  this.once('close', onclose);\n  this.once('upgrading', onupgrade);\n\n  transport.open();\n};\n\n/**\n * Called when connection is deemed open.\n *\n * @api public\n */\n\nSocket.prototype.onOpen = function () {\n\n  this.readyState = 'open';\n  Socket.priorWebsocketSuccess = 'websocket' === this.transport.name;\n  this.emit('open');\n  this.flush();\n\n  // we check for `readyState` in case an `open`\n  // listener already closed the socket\n  if ('open' === this.readyState && this.upgrade && this.transport.pause) {\n\n    for (var i = 0, l = this.upgrades.length; i < l; i++) {\n      this.probe(this.upgrades[i]);\n    }\n  }\n};\n\n/**\n * Handles a packet.\n *\n * @api private\n */\n\nSocket.prototype.onPacket = function (packet) {\n  if ('opening' === this.readyState || 'open' === this.readyState ||\n      'closing' === this.readyState) {\n\n\n    this.emit('packet', packet);\n\n    // Socket is live - any packet counts\n    this.emit('heartbeat');\n\n    switch (packet.type) {\n      case 'open':\n        this.onHandshake(JSON.parse(packet.data));\n        break;\n\n      case 'pong':\n        this.setPing();\n        this.emit('pong');\n        break;\n\n      case 'error':\n        var err = new Error('server error');\n        err.code = packet.data;\n        this.onError(err);\n        break;\n\n      case 'message':\n        this.emit('data', packet.data);\n        this.emit('message', packet.data);\n        break;\n    }\n  } else {\n\n  }\n};\n\n/**\n * Called upon handshake completion.\n *\n * @param {Object} handshake obj\n * @api private\n */\n\nSocket.prototype.onHandshake = function (data) {\n  this.emit('handshake', data);\n  this.id = data.sid;\n  this.transport.query.sid = data.sid;\n  this.upgrades = this.filterUpgrades(data.upgrades);\n  this.pingInterval = data.pingInterval;\n  this.pingTimeout = data.pingTimeout;\n  this.onOpen();\n  // In case open handler closes socket\n  if ('closed' === this.readyState) return;\n  this.setPing();\n\n  // Prolong liveness of socket on heartbeat\n  this.removeListener('heartbeat', this.onHeartbeat);\n  this.on('heartbeat', this.onHeartbeat);\n};\n\n/**\n * Resets ping timeout.\n *\n * @api private\n */\n\nSocket.prototype.onHeartbeat = function (timeout) {\n  clearTimeout(this.pingTimeoutTimer);\n  var self = this;\n  self.pingTimeoutTimer = setTimeout(function () {\n    if ('closed' === self.readyState) return;\n    self.onClose('ping timeout');\n  }, timeout || (self.pingInterval + self.pingTimeout));\n};\n\n/**\n * Pings server every `this.pingInterval` and expects response\n * within `this.pingTimeout` or closes connection.\n *\n * @api private\n */\n\nSocket.prototype.setPing = function () {\n  var self = this;\n  clearTimeout(self.pingIntervalTimer);\n  self.pingIntervalTimer = setTimeout(function () {\n\n    self.ping();\n    self.onHeartbeat(self.pingTimeout);\n  }, self.pingInterval);\n};\n\n/**\n* Sends a ping packet.\n*\n* @api private\n*/\n\nSocket.prototype.ping = function () {\n  var self = this;\n  this.sendPacket('ping', function () {\n    self.emit('ping');\n  });\n};\n\n/**\n * Called on `drain` event\n *\n * @api private\n */\n\nSocket.prototype.onDrain = function () {\n  this.writeBuffer.splice(0, this.prevBufferLen);\n\n  // setting prevBufferLen = 0 is very important\n  // for example, when upgrading, upgrade packet is sent over,\n  // and a nonzero prevBufferLen could cause problems on `drain`\n  this.prevBufferLen = 0;\n\n  if (0 === this.writeBuffer.length) {\n    this.emit('drain');\n  } else {\n    this.flush();\n  }\n};\n\n/**\n * Flush write buffers.\n *\n * @api private\n */\n\nSocket.prototype.flush = function () {\n  if ('closed' !== this.readyState && this.transport.writable &&\n    !this.upgrading && this.writeBuffer.length) {\n\n    this.transport.send(this.writeBuffer);\n    // keep track of current length of writeBuffer\n    // splice writeBuffer and callbackBuffer on `drain`\n    this.prevBufferLen = this.writeBuffer.length;\n    this.emit('flush');\n  }\n};\n\n/**\n * Sends a message.\n *\n * @param {String} message.\n * @param {Function} callback function.\n * @param {Object} options.\n * @return {Socket} for chaining.\n * @api public\n */\n\nSocket.prototype.write =\nSocket.prototype.send = function (msg, options, fn) {\n  this.sendPacket('message', msg, options, fn);\n  return this;\n};\n\n/**\n * Sends a packet.\n *\n * @param {String} packet type.\n * @param {String} data.\n * @param {Object} options.\n * @param {Function} callback function.\n * @api private\n */\n\nSocket.prototype.sendPacket = function (type, data, options, fn) {\n  if ('function' === typeof data) {\n    fn = data;\n    data = undefined;\n  }\n\n  if ('function' === typeof options) {\n    fn = options;\n    options = null;\n  }\n\n  if ('closing' === this.readyState || 'closed' === this.readyState) {\n    return;\n  }\n\n  options = options || {};\n  options.compress = false !== options.compress;\n\n  var packet = {\n    type: type,\n    data: data,\n    options: options\n  };\n  this.emit('packetCreate', packet);\n  this.writeBuffer.push(packet);\n  if (fn) this.once('flush', fn);\n  this.flush();\n};\n\n/**\n * Closes the connection.\n *\n * @api private\n */\n\nSocket.prototype.close = function () {\n  if ('opening' === this.readyState || 'open' === this.readyState) {\n    this.readyState = 'closing';\n\n    var self = this;\n\n    if (this.writeBuffer.length) {\n      this.once('drain', function () {\n        if (this.upgrading) {\n          waitForUpgrade();\n        } else {\n          close();\n        }\n      });\n    } else if (this.upgrading) {\n      waitForUpgrade();\n    } else {\n      close();\n    }\n  }\n\n  function close () {\n    self.onClose('forced close');\n\n    self.transport.close();\n  }\n\n  function cleanupAndClose () {\n    self.removeListener('upgrade', cleanupAndClose);\n    self.removeListener('upgradeError', cleanupAndClose);\n    close();\n  }\n\n  function waitForUpgrade () {\n    // wait for upgrade to finish since we can't send packets while pausing a transport\n    self.once('upgrade', cleanupAndClose);\n    self.once('upgradeError', cleanupAndClose);\n  }\n\n  return this;\n};\n\n/**\n * Called upon transport error\n *\n * @api private\n */\n\nSocket.prototype.onError = function (err) {\n\n  Socket.priorWebsocketSuccess = false;\n  this.emit('error', err);\n  this.onClose('transport error', err);\n};\n\n/**\n * Called upon transport close.\n *\n * @api private\n */\n\nSocket.prototype.onClose = function (reason, desc) {\n  if ('opening' === this.readyState || 'open' === this.readyState || 'closing' === this.readyState) {\n\n    var self = this;\n\n    // clear timers\n    clearTimeout(this.pingIntervalTimer);\n    clearTimeout(this.pingTimeoutTimer);\n\n    // stop event from firing again for transport\n    this.transport.removeAllListeners('close');\n\n    // ensure transport won't stay open\n    this.transport.close();\n\n    // ignore further transport communication\n    this.transport.removeAllListeners();\n\n    // set ready state\n    this.readyState = 'closed';\n\n    // clear session id\n    this.id = null;\n\n    // emit close event\n    this.emit('close', reason, desc);\n\n    // clean buffers after, so users can still\n    // grab the buffers on `close` event\n    self.writeBuffer = [];\n    self.prevBufferLen = 0;\n  }\n};\n\n/**\n * Filters upgrades, returning only those matching client transports.\n *\n * @param {Array} server upgrades\n * @api private\n *\n */\n\nSocket.prototype.filterUpgrades = function (upgrades) {\n  var filteredUpgrades = [];\n  for (var i = 0, j = upgrades.length; i < j; i++) {\n    if (~index(this.transports, upgrades[i])) filteredUpgrades.push(upgrades[i]);\n  }\n  return filteredUpgrades;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/socket.js\n// module id = 11\n// module chunks = 0", "/**\n * Module dependencies\n */\n\nvar XMLHttpRequest = require('xmlhttprequest-ssl');\nvar XHR = require('./polling-xhr');\nvar JSONP = require('./polling-jsonp');\nvar websocket = require('./websocket');\n\n/**\n * Export transports.\n */\n\nexports.polling = polling;\nexports.websocket = websocket;\n\n/**\n * Polling transport polymorphic constructor.\n * Decides on xhr vs jsonp based on feature detection.\n *\n * @api private\n */\n\nfunction polling (opts) {\n  var xhr;\n  var xd = false;\n  var xs = false;\n  var jsonp = false !== opts.jsonp;\n\n  if (typeof location !== 'undefined') {\n    var isSSL = 'https:' === location.protocol;\n    var port = location.port;\n\n    // some user agents have empty `location.port`\n    if (!port) {\n      port = isSSL ? 443 : 80;\n    }\n\n    xd = opts.hostname !== location.hostname || port !== opts.port;\n    xs = opts.secure !== isSSL;\n  }\n\n  opts.xdomain = xd;\n  opts.xscheme = xs;\n  xhr = new XMLHttpRequest(opts);\n\n  if ('open' in xhr && !opts.forceJSONP) {\n    return new XHR(opts);\n  } else {\n    if (!jsonp) throw new Error('JSONP disabled');\n    return new JSONP(opts);\n  }\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/transports/index.js\n// module id = 12\n// module chunks = 0", "// browser shim for xmlhttprequest module\n\nvar hasCORS = require('has-cors');\nvar globalThis = require('./globalThis');\n\nmodule.exports = function (opts) {\n  var xdomain = opts.xdomain;\n\n  // scheme must be same when usign XDomainRequest\n  // http://blogs.msdn.com/b/ieinternals/archive/2010/05/13/xdomainrequest-restrictions-limitations-and-workarounds.aspx\n  var xscheme = opts.xscheme;\n\n  // XDomainRequest has a flow of not sending cookie, therefore it should be disabled as a default.\n  // https://github.com/Automattic/engine.io-client/pull/217\n  var enablesXDR = opts.enablesXDR;\n\n  // XMLHttpRequest can be disabled on IE\n  try {\n    if ('undefined' !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n      return new XMLHttpRequest();\n    }\n  } catch (e) { }\n\n  // Use XDomainRequest for IE8 if enablesXDR is true\n  // because loading bar keeps flashing when using jsonp-polling\n  // https://github.com/yujiosaka/socke.io-ie8-loading-example\n  try {\n    if ('undefined' !== typeof XDomainRequest && !xscheme && enablesXDR) {\n      return new XDomainRequest();\n    }\n  } catch (e) { }\n\n  if (!xdomain) {\n    try {\n      return new globalThis[['Active'].concat('Object').join('X')]('Microsoft.XMLHTTP');\n    } catch (e) { }\n  }\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/xmlhttprequest.js\n// module id = 13\n// module chunks = 0", "\n/**\n * Module exports.\n *\n * Logic borrowed from Modernizr:\n *\n *   - https://github.com/Modernizr/Modernizr/blob/master/feature-detects/cors.js\n */\n\ntry {\n  module.exports = typeof XMLHttpRequest !== 'undefined' &&\n    'withCredentials' in new XMLHttpRequest();\n} catch (err) {\n  // if XMLHttp support is disabled in IE then it will throw\n  // when trying to create\n  module.exports = false;\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/has-cors/index.js\n// module id = 14\n// module chunks = 0", "module.exports = (function () {\n  if (typeof self !== 'undefined') {\n    return self;\n  } else if (typeof window !== 'undefined') {\n    return window;\n  } else {\n    return Function('return this')(); // eslint-disable-line no-new-func\n  }\n})();\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/globalThis.browser.js\n// module id = 15\n// module chunks = 0", "/* global attachEvent */\n\n/**\n * Module requirements.\n */\n\nvar XMLHttpRequest = require('xmlhttprequest-ssl');\nvar Polling = require('./polling');\nvar Emitter = require('component-emitter');\nvar inherit = require('component-inherit');\nvar debug = require('debug')('engine.io-client:polling-xhr');\nvar globalThis = require('../globalThis');\n\n/**\n * Module exports.\n */\n\nmodule.exports = XHR;\nmodule.exports.Request = Request;\n\n/**\n * Empty function\n */\n\nfunction empty () {}\n\n/**\n * XHR Polling constructor.\n *\n * @param {Object} opts\n * @api public\n */\n\nfunction XHR (opts) {\n  Polling.call(this, opts);\n  this.requestTimeout = opts.requestTimeout;\n  this.extraHeaders = opts.extraHeaders;\n\n  if (typeof location !== 'undefined') {\n    var isSSL = 'https:' === location.protocol;\n    var port = location.port;\n\n    // some user agents have empty `location.port`\n    if (!port) {\n      port = isSSL ? 443 : 80;\n    }\n\n    this.xd = (typeof location !== 'undefined' && opts.hostname !== location.hostname) ||\n      port !== opts.port;\n    this.xs = opts.secure !== isSSL;\n  }\n}\n\n/**\n * Inherits from Polling.\n */\n\ninherit(XHR, Polling);\n\n/**\n * XHR supports binary\n */\n\nXHR.prototype.supportsBinary = true;\n\n/**\n * Creates a request.\n *\n * @param {String} method\n * @api private\n */\n\nXHR.prototype.request = function (opts) {\n  opts = opts || {};\n  opts.uri = this.uri();\n  opts.xd = this.xd;\n  opts.xs = this.xs;\n  opts.agent = this.agent || false;\n  opts.supportsBinary = this.supportsBinary;\n  opts.enablesXDR = this.enablesXDR;\n  opts.withCredentials = this.withCredentials;\n\n  // SSL options for Node.js client\n  opts.pfx = this.pfx;\n  opts.key = this.key;\n  opts.passphrase = this.passphrase;\n  opts.cert = this.cert;\n  opts.ca = this.ca;\n  opts.ciphers = this.ciphers;\n  opts.rejectUnauthorized = this.rejectUnauthorized;\n  opts.requestTimeout = this.requestTimeout;\n\n  // other options for Node.js client\n  opts.extraHeaders = this.extraHeaders;\n\n  return new Request(opts);\n};\n\n/**\n * Sends data.\n *\n * @param {String} data to send.\n * @param {Function} called upon flush.\n * @api private\n */\n\nXHR.prototype.doWrite = function (data, fn) {\n  var isBinary = typeof data !== 'string' && data !== undefined;\n  var req = this.request({ method: 'POST', data: data, isBinary: isBinary });\n  var self = this;\n  req.on('success', fn);\n  req.on('error', function (err) {\n    self.onError('xhr post error', err);\n  });\n  this.sendXhr = req;\n};\n\n/**\n * Starts a poll cycle.\n *\n * @api private\n */\n\nXHR.prototype.doPoll = function () {\n\n  var req = this.request();\n  var self = this;\n  req.on('data', function (data) {\n    self.onData(data);\n  });\n  req.on('error', function (err) {\n    self.onError('xhr poll error', err);\n  });\n  this.pollXhr = req;\n};\n\n/**\n * Request constructor\n *\n * @param {Object} options\n * @api public\n */\n\nfunction Request (opts) {\n  this.method = opts.method || 'GET';\n  this.uri = opts.uri;\n  this.xd = !!opts.xd;\n  this.xs = !!opts.xs;\n  this.async = false !== opts.async;\n  this.data = undefined !== opts.data ? opts.data : null;\n  this.agent = opts.agent;\n  this.isBinary = opts.isBinary;\n  this.supportsBinary = opts.supportsBinary;\n  this.enablesXDR = opts.enablesXDR;\n  this.withCredentials = opts.withCredentials;\n  this.requestTimeout = opts.requestTimeout;\n\n  // SSL options for Node.js client\n  this.pfx = opts.pfx;\n  this.key = opts.key;\n  this.passphrase = opts.passphrase;\n  this.cert = opts.cert;\n  this.ca = opts.ca;\n  this.ciphers = opts.ciphers;\n  this.rejectUnauthorized = opts.rejectUnauthorized;\n\n  // other options for Node.js client\n  this.extraHeaders = opts.extraHeaders;\n\n  this.create();\n}\n\n/**\n * Mix in `Emitter`.\n */\n\nEmitter(Request.prototype);\n\n/**\n * Creates the XHR object and sends the request.\n *\n * @api private\n */\n\nRequest.prototype.create = function () {\n  var opts = { agent: this.agent, xdomain: this.xd, xscheme: this.xs, enablesXDR: this.enablesXDR };\n\n  // SSL options for Node.js client\n  opts.pfx = this.pfx;\n  opts.key = this.key;\n  opts.passphrase = this.passphrase;\n  opts.cert = this.cert;\n  opts.ca = this.ca;\n  opts.ciphers = this.ciphers;\n  opts.rejectUnauthorized = this.rejectUnauthorized;\n\n  var xhr = this.xhr = new XMLHttpRequest(opts);\n  var self = this;\n\n  try {\n\n    xhr.open(this.method, this.uri, this.async);\n    try {\n      if (this.extraHeaders) {\n        xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n        for (var i in this.extraHeaders) {\n          if (this.extraHeaders.hasOwnProperty(i)) {\n            xhr.setRequestHeader(i, this.extraHeaders[i]);\n          }\n        }\n      }\n    } catch (e) {}\n\n    if ('POST' === this.method) {\n      try {\n        if (this.isBinary) {\n          xhr.setRequestHeader('Content-type', 'application/octet-stream');\n        } else {\n          xhr.setRequestHeader('Content-type', 'text/plain;charset=UTF-8');\n        }\n      } catch (e) {}\n    }\n\n    try {\n      xhr.setRequestHeader('Accept', '*/*');\n    } catch (e) {}\n\n    // ie6 check\n    if ('withCredentials' in xhr) {\n      xhr.withCredentials = this.withCredentials;\n    }\n\n    if (this.requestTimeout) {\n      xhr.timeout = this.requestTimeout;\n    }\n\n    if (this.hasXDR()) {\n      xhr.onload = function () {\n        self.onLoad();\n      };\n      xhr.onerror = function () {\n        self.onError(xhr.responseText);\n      };\n    } else {\n      xhr.onreadystatechange = function () {\n        if (xhr.readyState === 2) {\n          try {\n            var contentType = xhr.getResponseHeader('Content-Type');\n            if (self.supportsBinary && contentType === 'application/octet-stream' || contentType === 'application/octet-stream; charset=UTF-8') {\n              xhr.responseType = 'arraybuffer';\n            }\n          } catch (e) {}\n        }\n        if (4 !== xhr.readyState) return;\n        if (200 === xhr.status || 1223 === xhr.status) {\n          self.onLoad();\n        } else {\n          // make sure the `error` event handler that's user-set\n          // does not throw in the same tick and gets caught here\n          setTimeout(function () {\n            self.onError(typeof xhr.status === 'number' ? xhr.status : 0);\n          }, 0);\n        }\n      };\n    }\n\n\n    xhr.send(this.data);\n  } catch (e) {\n    // Need to defer since .create() is called directly fhrom the constructor\n    // and thus the 'error' event can only be only bound *after* this exception\n    // occurs.  Therefore, also, we cannot throw here at all.\n    setTimeout(function () {\n      self.onError(e);\n    }, 0);\n    return;\n  }\n\n  if (typeof document !== 'undefined') {\n    this.index = Request.requestsCount++;\n    Request.requests[this.index] = this;\n  }\n};\n\n/**\n * Called upon successful response.\n *\n * @api private\n */\n\nRequest.prototype.onSuccess = function () {\n  this.emit('success');\n  this.cleanup();\n};\n\n/**\n * Called if we have data.\n *\n * @api private\n */\n\nRequest.prototype.onData = function (data) {\n  this.emit('data', data);\n  this.onSuccess();\n};\n\n/**\n * Called upon error.\n *\n * @api private\n */\n\nRequest.prototype.onError = function (err) {\n  this.emit('error', err);\n  this.cleanup(true);\n};\n\n/**\n * Cleans up house.\n *\n * @api private\n */\n\nRequest.prototype.cleanup = function (fromError) {\n  if ('undefined' === typeof this.xhr || null === this.xhr) {\n    return;\n  }\n  // xmlhttprequest\n  if (this.hasXDR()) {\n    this.xhr.onload = this.xhr.onerror = empty;\n  } else {\n    this.xhr.onreadystatechange = empty;\n  }\n\n  if (fromError) {\n    try {\n      this.xhr.abort();\n    } catch (e) {}\n  }\n\n  if (typeof document !== 'undefined') {\n    delete Request.requests[this.index];\n  }\n\n  this.xhr = null;\n};\n\n/**\n * Called upon load.\n *\n * @api private\n */\n\nRequest.prototype.onLoad = function () {\n  var data;\n  try {\n    var contentType;\n    try {\n      contentType = this.xhr.getResponseHeader('Content-Type');\n    } catch (e) {}\n    if (contentType === 'application/octet-stream' || contentType === 'application/octet-stream; charset=UTF-8') {\n      data = this.xhr.response || this.xhr.responseText;\n    } else {\n      data = this.xhr.responseText;\n    }\n  } catch (e) {\n    this.onError(e);\n  }\n  if (null != data) {\n    this.onData(data);\n  }\n};\n\n/**\n * Check if it has XDomainRequest.\n *\n * @api private\n */\n\nRequest.prototype.hasXDR = function () {\n  return typeof XDomainRequest !== 'undefined' && !this.xs && this.enablesXDR;\n};\n\n/**\n * Aborts the request.\n *\n * @api public\n */\n\nRequest.prototype.abort = function () {\n  this.cleanup();\n};\n\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\n\nRequest.requestsCount = 0;\nRequest.requests = {};\n\nif (typeof document !== 'undefined') {\n  if (typeof attachEvent === 'function') {\n    attachEvent('onunload', unloadHandler);\n  } else if (typeof addEventListener === 'function') {\n    var terminationEvent = 'onpagehide' in globalThis ? 'pagehide' : 'unload';\n    addEventListener(terminationEvent, unloadHandler, false);\n  }\n}\n\nfunction unloadHandler () {\n  for (var i in Request.requests) {\n    if (Request.requests.hasOwnProperty(i)) {\n      Request.requests[i].abort();\n    }\n  }\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/transports/polling-xhr.js\n// module id = 16\n// module chunks = 0", "/**\n * Module dependencies.\n */\n\nvar Transport = require('../transport');\nvar parseqs = require('parseqs');\nvar parser = require('engine.io-parser');\nvar inherit = require('component-inherit');\nvar yeast = require('yeast');\nvar debug = require('debug')('engine.io-client:polling');\n\n/**\n * Module exports.\n */\n\nmodule.exports = Polling;\n\n/**\n * Is XHR2 supported?\n */\n\nvar hasXHR2 = (function () {\n  var XMLHttpRequest = require('xmlhttprequest-ssl');\n  var xhr = new XMLHttpRequest({ xdomain: false });\n  return null != xhr.responseType;\n})();\n\n/**\n * Polling interface.\n *\n * @param {Object} opts\n * @api private\n */\n\nfunction Polling (opts) {\n  var forceBase64 = (opts && opts.forceBase64);\n  if (!hasXHR2 || forceBase64) {\n    this.supportsBinary = false;\n  }\n  Transport.call(this, opts);\n}\n\n/**\n * Inherits from Transport.\n */\n\ninherit(Polling, Transport);\n\n/**\n * Transport name.\n */\n\nPolling.prototype.name = 'polling';\n\n/**\n * Opens the socket (triggers polling). We write a PING message to determine\n * when the transport is open.\n *\n * @api private\n */\n\nPolling.prototype.doOpen = function () {\n  this.poll();\n};\n\n/**\n * Pauses polling.\n *\n * @param {Function} callback upon buffers are flushed and transport is paused\n * @api private\n */\n\nPolling.prototype.pause = function (onPause) {\n  var self = this;\n\n  this.readyState = 'pausing';\n\n  function pause () {\n\n    self.readyState = 'paused';\n    onPause();\n  }\n\n  if (this.polling || !this.writable) {\n    var total = 0;\n\n    if (this.polling) {\n\n      total++;\n      this.once('pollComplete', function () {\n\n        --total || pause();\n      });\n    }\n\n    if (!this.writable) {\n\n      total++;\n      this.once('drain', function () {\n\n        --total || pause();\n      });\n    }\n  } else {\n    pause();\n  }\n};\n\n/**\n * Starts polling cycle.\n *\n * @api public\n */\n\nPolling.prototype.poll = function () {\n\n  this.polling = true;\n  this.doPoll();\n  this.emit('poll');\n};\n\n/**\n * Overloads onData to detect payloads.\n *\n * @api private\n */\n\nPolling.prototype.onData = function (data) {\n  var self = this;\n\n  var callback = function (packet, index, total) {\n    // if its the first message we consider the transport open\n    if ('opening' === self.readyState && packet.type === 'open') {\n      self.onOpen();\n    }\n\n    // if its a close packet, we close the ongoing requests\n    if ('close' === packet.type) {\n      self.onClose();\n      return false;\n    }\n\n    // otherwise bypass onData and handle the message\n    self.onPacket(packet);\n  };\n\n  // decode payload\n  parser.decodePayload(data, this.socket.binaryType, callback);\n\n  // if an event did not trigger closing\n  if ('closed' !== this.readyState) {\n    // if we got data we're not polling\n    this.polling = false;\n    this.emit('pollComplete');\n\n    if ('open' === this.readyState) {\n      this.poll();\n    } else {\n\n    }\n  }\n};\n\n/**\n * For polling, send a close packet.\n *\n * @api private\n */\n\nPolling.prototype.doClose = function () {\n  var self = this;\n\n  function close () {\n\n    self.write([{ type: 'close' }]);\n  }\n\n  if ('open' === this.readyState) {\n\n    close();\n  } else {\n    // in case we're trying to close while\n    // handshaking is in progress (GH-164)\n\n    this.once('open', close);\n  }\n};\n\n/**\n * Writes a packets payload.\n *\n * @param {Array} data packets\n * @param {Function} drain callback\n * @api private\n */\n\nPolling.prototype.write = function (packets) {\n  var self = this;\n  this.writable = false;\n  var callbackfn = function () {\n    self.writable = true;\n    self.emit('drain');\n  };\n\n  parser.encodePayload(packets, this.supportsBinary, function (data) {\n    self.doWrite(data, callbackfn);\n  });\n};\n\n/**\n * Generates uri for connection.\n *\n * @api private\n */\n\nPolling.prototype.uri = function () {\n  var query = this.query || {};\n  var schema = this.secure ? 'https' : 'http';\n  var port = '';\n\n  // cache busting is forced\n  if (false !== this.timestampRequests) {\n    query[this.timestampParam] = yeast();\n  }\n\n  if (!this.supportsBinary && !query.sid) {\n    query.b64 = 1;\n  }\n\n  query = parseqs.encode(query);\n\n  // avoid port if default for schema\n  if (this.port && (('https' === schema && Number(this.port) !== 443) ||\n     ('http' === schema && Number(this.port) !== 80))) {\n    port = ':' + this.port;\n  }\n\n  // prepend ? to query\n  if (query.length) {\n    query = '?' + query;\n  }\n\n  var ipv6 = this.hostname.indexOf(':') !== -1;\n  return schema + '://' + (ipv6 ? '[' + this.hostname + ']' : this.hostname) + port + this.path + query;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/transports/polling.js\n// module id = 17\n// module chunks = 0", "/**\n * Module dependencies.\n */\n\nvar parser = require('engine.io-parser');\nvar Emitter = require('component-emitter');\n\n/**\n * Module exports.\n */\n\nmodule.exports = Transport;\n\n/**\n * Transport abstract constructor.\n *\n * @param {Object} options.\n * @api private\n */\n\nfunction Transport (opts) {\n  this.path = opts.path;\n  this.hostname = opts.hostname;\n  this.port = opts.port;\n  this.secure = opts.secure;\n  this.query = opts.query;\n  this.timestampParam = opts.timestampParam;\n  this.timestampRequests = opts.timestampRequests;\n  this.readyState = '';\n  this.agent = opts.agent || false;\n  this.socket = opts.socket;\n  this.enablesXDR = opts.enablesXDR;\n  this.withCredentials = opts.withCredentials;\n\n  // SSL options for Node.js client\n  this.pfx = opts.pfx;\n  this.key = opts.key;\n  this.passphrase = opts.passphrase;\n  this.cert = opts.cert;\n  this.ca = opts.ca;\n  this.ciphers = opts.ciphers;\n  this.rejectUnauthorized = opts.rejectUnauthorized;\n  this.forceNode = opts.forceNode;\n\n  // results of ReactNative environment detection\n  this.isReactNative = opts.isReactNative;\n\n  // other options for Node.js client\n  this.extraHeaders = opts.extraHeaders;\n  this.localAddress = opts.localAddress;\n}\n\n/**\n * Mix in `Emitter`.\n */\n\nEmitter(Transport.prototype);\n\n/**\n * Emits an error.\n *\n * @param {String} str\n * @return {Transport} for chaining\n * @api public\n */\n\nTransport.prototype.onError = function (msg, desc) {\n  var err = new Error(msg);\n  err.type = 'TransportError';\n  err.description = desc;\n  this.emit('error', err);\n  return this;\n};\n\n/**\n * Opens the transport.\n *\n * @api public\n */\n\nTransport.prototype.open = function () {\n  if ('closed' === this.readyState || '' === this.readyState) {\n    this.readyState = 'opening';\n    this.doOpen();\n  }\n\n  return this;\n};\n\n/**\n * Closes the transport.\n *\n * @api private\n */\n\nTransport.prototype.close = function () {\n  if ('opening' === this.readyState || 'open' === this.readyState) {\n    this.doClose();\n    this.onClose();\n  }\n\n  return this;\n};\n\n/**\n * Sends multiple packets.\n *\n * @param {Array} packets\n * @api private\n */\n\nTransport.prototype.send = function (packets) {\n  if ('open' === this.readyState) {\n    this.write(packets);\n  } else {\n    throw new Error('Transport not open');\n  }\n};\n\n/**\n * Called upon open\n *\n * @api private\n */\n\nTransport.prototype.onOpen = function () {\n  this.readyState = 'open';\n  this.writable = true;\n  this.emit('open');\n};\n\n/**\n * Called with data.\n *\n * @param {String} data\n * @api private\n */\n\nTransport.prototype.onData = function (data) {\n  var packet = parser.decodePacket(data, this.socket.binaryType);\n  this.onPacket(packet);\n};\n\n/**\n * Called with a decoded packet.\n */\n\nTransport.prototype.onPacket = function (packet) {\n  this.emit('packet', packet);\n};\n\n/**\n * Called upon close.\n *\n * @api private\n */\n\nTransport.prototype.onClose = function () {\n  this.readyState = 'closed';\n  this.emit('close');\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/transport.js\n// module id = 18\n// module chunks = 0", "/**\n * Module dependencies.\n */\n\nvar keys = require('./keys');\nvar hasBinary = require('has-binary2');\nvar sliceBuffer = require('arraybuffer.slice');\nvar after = require('after');\nvar utf8 = require('./utf8');\n\nvar base64encoder;\nif (typeof ArrayBuffer !== 'undefined') {\n  base64encoder = require('base64-arraybuffer');\n}\n\n/**\n * Check if we are running an android browser. That requires us to use\n * ArrayBuffer with polling transports...\n *\n * http://ghinda.net/jpeg-blob-ajax-android/\n */\n\nvar isAndroid = typeof navigator !== 'undefined' && /Android/i.test(navigator.userAgent);\n\n/**\n * Check if we are running in PhantomJS.\n * Uploading a Blob with PhantomJS does not work correctly, as reported here:\n * https://github.com/ariya/phantomjs/issues/11395\n * @type boolean\n */\nvar isPhantomJS = typeof navigator !== 'undefined' && /PhantomJS/i.test(navigator.userAgent);\n\n/**\n * When true, avoids using Blobs to encode payloads.\n * @type boolean\n */\nvar dontSendBlobs = isAndroid || isPhantomJS;\n\n/**\n * Current protocol version.\n */\n\nexports.protocol = 3;\n\n/**\n * Packet types.\n */\n\nvar packets = exports.packets = {\n    open:     0    // non-ws\n  , close:    1    // non-ws\n  , ping:     2\n  , pong:     3\n  , message:  4\n  , upgrade:  5\n  , noop:     6\n};\n\nvar packetslist = keys(packets);\n\n/**\n * Premade error packet.\n */\n\nvar err = { type: 'error', data: 'parser error' };\n\n/**\n * Create a blob api even for blob builder when vendor prefixes exist\n */\n\nvar Blob = require('blob');\n\n/**\n * Encodes a packet.\n *\n *     <packet type id> [ <data> ]\n *\n * Example:\n *\n *     5hello world\n *     3\n *     4\n *\n * Binary is encoded in an identical principle\n *\n * @api private\n */\n\nexports.encodePacket = function (packet, supportsBinary, utf8encode, callback) {\n  if (typeof supportsBinary === 'function') {\n    callback = supportsBinary;\n    supportsBinary = false;\n  }\n\n  if (typeof utf8encode === 'function') {\n    callback = utf8encode;\n    utf8encode = null;\n  }\n\n  var data = (packet.data === undefined)\n    ? undefined\n    : packet.data.buffer || packet.data;\n\n  if (typeof ArrayBuffer !== 'undefined' && data instanceof ArrayBuffer) {\n    return encodeArrayBuffer(packet, supportsBinary, callback);\n  } else if (typeof Blob !== 'undefined' && data instanceof Blob) {\n    return encodeBlob(packet, supportsBinary, callback);\n  }\n\n  // might be an object with { base64: true, data: dataAsBase64String }\n  if (data && data.base64) {\n    return encodeBase64Object(packet, callback);\n  }\n\n  // Sending data as a utf-8 string\n  var encoded = packets[packet.type];\n\n  // data fragment is optional\n  if (undefined !== packet.data) {\n    encoded += utf8encode ? utf8.encode(String(packet.data), { strict: false }) : String(packet.data);\n  }\n\n  return callback('' + encoded);\n\n};\n\nfunction encodeBase64Object(packet, callback) {\n  // packet data is an object { base64: true, data: dataAsBase64String }\n  var message = 'b' + exports.packets[packet.type] + packet.data.data;\n  return callback(message);\n}\n\n/**\n * Encode packet helpers for binary types\n */\n\nfunction encodeArrayBuffer(packet, supportsBinary, callback) {\n  if (!supportsBinary) {\n    return exports.encodeBase64Packet(packet, callback);\n  }\n\n  var data = packet.data;\n  var contentArray = new Uint8Array(data);\n  var resultBuffer = new Uint8Array(1 + data.byteLength);\n\n  resultBuffer[0] = packets[packet.type];\n  for (var i = 0; i < contentArray.length; i++) {\n    resultBuffer[i+1] = contentArray[i];\n  }\n\n  return callback(resultBuffer.buffer);\n}\n\nfunction encodeBlobAsArrayBuffer(packet, supportsBinary, callback) {\n  if (!supportsBinary) {\n    return exports.encodeBase64Packet(packet, callback);\n  }\n\n  var fr = new FileReader();\n  fr.onload = function() {\n    exports.encodePacket({ type: packet.type, data: fr.result }, supportsBinary, true, callback);\n  };\n  return fr.readAsArrayBuffer(packet.data);\n}\n\nfunction encodeBlob(packet, supportsBinary, callback) {\n  if (!supportsBinary) {\n    return exports.encodeBase64Packet(packet, callback);\n  }\n\n  if (dontSendBlobs) {\n    return encodeBlobAsArrayBuffer(packet, supportsBinary, callback);\n  }\n\n  var length = new Uint8Array(1);\n  length[0] = packets[packet.type];\n  var blob = new Blob([length.buffer, packet.data]);\n\n  return callback(blob);\n}\n\n/**\n * Encodes a packet with binary data in a base64 string\n *\n * @param {Object} packet, has `type` and `data`\n * @return {String} base64 encoded message\n */\n\nexports.encodeBase64Packet = function(packet, callback) {\n  var message = 'b' + exports.packets[packet.type];\n  if (typeof Blob !== 'undefined' && packet.data instanceof Blob) {\n    var fr = new FileReader();\n    fr.onload = function() {\n      var b64 = fr.result.split(',')[1];\n      callback(message + b64);\n    };\n    return fr.readAsDataURL(packet.data);\n  }\n\n  var b64data;\n  try {\n    b64data = String.fromCharCode.apply(null, new Uint8Array(packet.data));\n  } catch (e) {\n    // iPhone Safari doesn't let you apply with typed arrays\n    var typed = new Uint8Array(packet.data);\n    var basic = new Array(typed.length);\n    for (var i = 0; i < typed.length; i++) {\n      basic[i] = typed[i];\n    }\n    b64data = String.fromCharCode.apply(null, basic);\n  }\n  message += btoa(b64data);\n  return callback(message);\n};\n\n/**\n * Decodes a packet. Changes format to Blob if requested.\n *\n * @return {Object} with `type` and `data` (if any)\n * @api private\n */\n\nexports.decodePacket = function (data, binaryType, utf8decode) {\n  if (data === undefined) {\n    return err;\n  }\n  // String data\n  if (typeof data === 'string') {\n    if (data.charAt(0) === 'b') {\n      return exports.decodeBase64Packet(data.substr(1), binaryType);\n    }\n\n    if (utf8decode) {\n      data = tryDecode(data);\n      if (data === false) {\n        return err;\n      }\n    }\n    var type = data.charAt(0);\n\n    if (Number(type) != type || !packetslist[type]) {\n      return err;\n    }\n\n    if (data.length > 1) {\n      return { type: packetslist[type], data: data.substring(1) };\n    } else {\n      return { type: packetslist[type] };\n    }\n  }\n\n  var asArray = new Uint8Array(data);\n  var type = asArray[0];\n  var rest = sliceBuffer(data, 1);\n  if (Blob && binaryType === 'blob') {\n    rest = new Blob([rest]);\n  }\n  return { type: packetslist[type], data: rest };\n};\n\nfunction tryDecode(data) {\n  try {\n    data = utf8.decode(data, { strict: false });\n  } catch (e) {\n    return false;\n  }\n  return data;\n}\n\n/**\n * Decodes a packet encoded in a base64 string\n *\n * @param {String} base64 encoded message\n * @return {Object} with `type` and `data` (if any)\n */\n\nexports.decodeBase64Packet = function(msg, binaryType) {\n  var type = packetslist[msg.charAt(0)];\n  if (!base64encoder) {\n    return { type: type, data: { base64: true, data: msg.substr(1) } };\n  }\n\n  var data = base64encoder.decode(msg.substr(1));\n\n  if (binaryType === 'blob' && Blob) {\n    data = new Blob([data]);\n  }\n\n  return { type: type, data: data };\n};\n\n/**\n * Encodes multiple messages (payload).\n *\n *     <length>:data\n *\n * Example:\n *\n *     11:hello world2:hi\n *\n * If any contents are binary, they will be encoded as base64 strings. Base64\n * encoded strings are marked with a b before the length specifier\n *\n * @param {Array} packets\n * @api private\n */\n\nexports.encodePayload = function (packets, supportsBinary, callback) {\n  if (typeof supportsBinary === 'function') {\n    callback = supportsBinary;\n    supportsBinary = null;\n  }\n\n  var isBinary = hasBinary(packets);\n\n  if (supportsBinary && isBinary) {\n    if (Blob && !dontSendBlobs) {\n      return exports.encodePayloadAsBlob(packets, callback);\n    }\n\n    return exports.encodePayloadAsArrayBuffer(packets, callback);\n  }\n\n  if (!packets.length) {\n    return callback('0:');\n  }\n\n  function setLengthHeader(message) {\n    return message.length + ':' + message;\n  }\n\n  function encodeOne(packet, doneCallback) {\n    exports.encodePacket(packet, !isBinary ? false : supportsBinary, false, function(message) {\n      doneCallback(null, setLengthHeader(message));\n    });\n  }\n\n  map(packets, encodeOne, function(err, results) {\n    return callback(results.join(''));\n  });\n};\n\n/**\n * Async array map using after\n */\n\nfunction map(ary, each, done) {\n  var result = new Array(ary.length);\n  var next = after(ary.length, done);\n\n  var eachWithIndex = function(i, el, cb) {\n    each(el, function(error, msg) {\n      result[i] = msg;\n      cb(error, result);\n    });\n  };\n\n  for (var i = 0; i < ary.length; i++) {\n    eachWithIndex(i, ary[i], next);\n  }\n}\n\n/*\n * Decodes data when a payload is maybe expected. Possible binary contents are\n * decoded from their base64 representation\n *\n * @param {String} data, callback method\n * @api public\n */\n\nexports.decodePayload = function (data, binaryType, callback) {\n  if (typeof data !== 'string') {\n    return exports.decodePayloadAsBinary(data, binaryType, callback);\n  }\n\n  if (typeof binaryType === 'function') {\n    callback = binaryType;\n    binaryType = null;\n  }\n\n  var packet;\n  if (data === '') {\n    // parser error - ignoring payload\n    return callback(err, 0, 1);\n  }\n\n  var length = '', n, msg;\n\n  for (var i = 0, l = data.length; i < l; i++) {\n    var chr = data.charAt(i);\n\n    if (chr !== ':') {\n      length += chr;\n      continue;\n    }\n\n    if (length === '' || (length != (n = Number(length)))) {\n      // parser error - ignoring payload\n      return callback(err, 0, 1);\n    }\n\n    msg = data.substr(i + 1, n);\n\n    if (length != msg.length) {\n      // parser error - ignoring payload\n      return callback(err, 0, 1);\n    }\n\n    if (msg.length) {\n      packet = exports.decodePacket(msg, binaryType, false);\n\n      if (err.type === packet.type && err.data === packet.data) {\n        // parser error in individual packet - ignoring payload\n        return callback(err, 0, 1);\n      }\n\n      var ret = callback(packet, i + n, l);\n      if (false === ret) return;\n    }\n\n    // advance cursor\n    i += n;\n    length = '';\n  }\n\n  if (length !== '') {\n    // parser error - ignoring payload\n    return callback(err, 0, 1);\n  }\n\n};\n\n/**\n * Encodes multiple messages (payload) as binary.\n *\n * <1 = binary, 0 = string><number from 0-9><number from 0-9>[...]<number\n * 255><data>\n *\n * Example:\n * 1 3 255 1 2 3, if the binary contents are interpreted as 8 bit integers\n *\n * @param {Array} packets\n * @return {ArrayBuffer} encoded payload\n * @api private\n */\n\nexports.encodePayloadAsArrayBuffer = function(packets, callback) {\n  if (!packets.length) {\n    return callback(new ArrayBuffer(0));\n  }\n\n  function encodeOne(packet, doneCallback) {\n    exports.encodePacket(packet, true, true, function(data) {\n      return doneCallback(null, data);\n    });\n  }\n\n  map(packets, encodeOne, function(err, encodedPackets) {\n    var totalLength = encodedPackets.reduce(function(acc, p) {\n      var len;\n      if (typeof p === 'string'){\n        len = p.length;\n      } else {\n        len = p.byteLength;\n      }\n      return acc + len.toString().length + len + 2; // string/binary identifier + separator = 2\n    }, 0);\n\n    var resultArray = new Uint8Array(totalLength);\n\n    var bufferIndex = 0;\n    encodedPackets.forEach(function(p) {\n      var isString = typeof p === 'string';\n      var ab = p;\n      if (isString) {\n        var view = new Uint8Array(p.length);\n        for (var i = 0; i < p.length; i++) {\n          view[i] = p.charCodeAt(i);\n        }\n        ab = view.buffer;\n      }\n\n      if (isString) { // not true binary\n        resultArray[bufferIndex++] = 0;\n      } else { // true binary\n        resultArray[bufferIndex++] = 1;\n      }\n\n      var lenStr = ab.byteLength.toString();\n      for (var i = 0; i < lenStr.length; i++) {\n        resultArray[bufferIndex++] = parseInt(lenStr[i]);\n      }\n      resultArray[bufferIndex++] = 255;\n\n      var view = new Uint8Array(ab);\n      for (var i = 0; i < view.length; i++) {\n        resultArray[bufferIndex++] = view[i];\n      }\n    });\n\n    return callback(resultArray.buffer);\n  });\n};\n\n/**\n * Encode as Blob\n */\n\nexports.encodePayloadAsBlob = function(packets, callback) {\n  function encodeOne(packet, doneCallback) {\n    exports.encodePacket(packet, true, true, function(encoded) {\n      var binaryIdentifier = new Uint8Array(1);\n      binaryIdentifier[0] = 1;\n      if (typeof encoded === 'string') {\n        var view = new Uint8Array(encoded.length);\n        for (var i = 0; i < encoded.length; i++) {\n          view[i] = encoded.charCodeAt(i);\n        }\n        encoded = view.buffer;\n        binaryIdentifier[0] = 0;\n      }\n\n      var len = (encoded instanceof ArrayBuffer)\n        ? encoded.byteLength\n        : encoded.size;\n\n      var lenStr = len.toString();\n      var lengthAry = new Uint8Array(lenStr.length + 1);\n      for (var i = 0; i < lenStr.length; i++) {\n        lengthAry[i] = parseInt(lenStr[i]);\n      }\n      lengthAry[lenStr.length] = 255;\n\n      if (Blob) {\n        var blob = new Blob([binaryIdentifier.buffer, lengthAry.buffer, encoded]);\n        doneCallback(null, blob);\n      }\n    });\n  }\n\n  map(packets, encodeOne, function(err, results) {\n    return callback(new Blob(results));\n  });\n};\n\n/*\n * Decodes data when a payload is maybe expected. Strings are decoded by\n * interpreting each byte as a key code for entries marked to start with 0. See\n * description of encodePayloadAsBinary\n *\n * @param {ArrayBuffer} data, callback method\n * @api public\n */\n\nexports.decodePayloadAsBinary = function (data, binaryType, callback) {\n  if (typeof binaryType === 'function') {\n    callback = binaryType;\n    binaryType = null;\n  }\n\n  var bufferTail = data;\n  var buffers = [];\n\n  while (bufferTail.byteLength > 0) {\n    var tailArray = new Uint8Array(bufferTail);\n    var isString = tailArray[0] === 0;\n    var msgLength = '';\n\n    for (var i = 1; ; i++) {\n      if (tailArray[i] === 255) break;\n\n      // 310 = char length of Number.MAX_VALUE\n      if (msgLength.length > 310) {\n        return callback(err, 0, 1);\n      }\n\n      msgLength += tailArray[i];\n    }\n\n    bufferTail = sliceBuffer(bufferTail, 2 + msgLength.length);\n    msgLength = parseInt(msgLength);\n\n    var msg = sliceBuffer(bufferTail, 0, msgLength);\n    if (isString) {\n      try {\n        msg = String.fromCharCode.apply(null, new Uint8Array(msg));\n      } catch (e) {\n        // iPhone Safari doesn't let you apply to typed arrays\n        var typed = new Uint8Array(msg);\n        msg = '';\n        for (var i = 0; i < typed.length; i++) {\n          msg += String.fromCharCode(typed[i]);\n        }\n      }\n    }\n\n    buffers.push(msg);\n    bufferTail = sliceBuffer(bufferTail, msgLength);\n  }\n\n  var total = buffers.length;\n  buffers.forEach(function(buffer, i) {\n    callback(exports.decodePacket(buffer, binaryType, true), i, total);\n  });\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-parser/lib/browser.js\n// module id = 19\n// module chunks = 0", "\n/**\n * Gets the keys for an object.\n *\n * @return {Array} keys\n * @api private\n */\n\nmodule.exports = Object.keys || function keys (obj){\n  var arr = [];\n  var has = Object.prototype.hasOwnProperty;\n\n  for (var i in obj) {\n    if (has.call(obj, i)) {\n      arr.push(i);\n    }\n  }\n  return arr;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-parser/lib/keys.js\n// module id = 20\n// module chunks = 0", "/* global Blob File */\n\n/*\n * Module requirements.\n */\n\nvar isArray = require('isarray');\n\nvar toString = Object.prototype.toString;\nvar withNativeBlob = typeof Blob === 'function' ||\n                        typeof Blob !== 'undefined' && toString.call(Blob) === '[object BlobConstructor]';\nvar withNativeFile = typeof File === 'function' ||\n                        typeof File !== 'undefined' && toString.call(File) === '[object FileConstructor]';\n\n/**\n * Module exports.\n */\n\nmodule.exports = hasBinary;\n\n/**\n * Checks for binary data.\n *\n * Supports Buffer, ArrayBuffer, Blob and File.\n *\n * @param {Object} anything\n * @api public\n */\n\nfunction hasBinary (obj) {\n  if (!obj || typeof obj !== 'object') {\n    return false;\n  }\n\n  if (isArray(obj)) {\n    for (var i = 0, l = obj.length; i < l; i++) {\n      if (hasBinary(obj[i])) {\n        return true;\n      }\n    }\n    return false;\n  }\n\n  if ((typeof Buffer === 'function' && Buffer.isBuffer && Buffer.isBuffer(obj)) ||\n    (typeof ArrayBuffer === 'function' && obj instanceof ArrayBuffer) ||\n    (withNativeBlob && obj instanceof Blob) ||\n    (withNativeFile && obj instanceof File)\n  ) {\n    return true;\n  }\n\n  // see: https://github.com/Automattic/has-binary/pull/4\n  if (obj.toJSON && typeof obj.toJSON === 'function' && arguments.length === 1) {\n    return hasBinary(obj.toJSON(), true);\n  }\n\n  for (var key in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, key) && hasBinary(obj[key])) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/has-binary2/index.js\n// module id = 21\n// module chunks = 0", "/**\n * An abstraction for slicing an arraybuffer even when\n * ArrayBuffer.prototype.slice is not supported\n *\n * @api public\n */\n\nmodule.exports = function(arraybuffer, start, end) {\n  var bytes = arraybuffer.byteLength;\n  start = start || 0;\n  end = end || bytes;\n\n  if (arraybuffer.slice) { return arraybuffer.slice(start, end); }\n\n  if (start < 0) { start += bytes; }\n  if (end < 0) { end += bytes; }\n  if (end > bytes) { end = bytes; }\n\n  if (start >= bytes || start >= end || bytes === 0) {\n    return new ArrayBuffer(0);\n  }\n\n  var abv = new Uint8Array(arraybuffer);\n  var result = new Uint8Array(end - start);\n  for (var i = start, ii = 0; i < end; i++, ii++) {\n    result[ii] = abv[i];\n  }\n  return result.buffer;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/arraybuffer.slice/index.js\n// module id = 22\n// module chunks = 0", "module.exports = after\n\nfunction after(count, callback, err_cb) {\n    var bail = false\n    err_cb = err_cb || noop\n    proxy.count = count\n\n    return (count === 0) ? callback() : proxy\n\n    function proxy(err, result) {\n        if (proxy.count <= 0) {\n            throw new Error('after called too many times')\n        }\n        --proxy.count\n\n        // after first error, rest are passed to err_cb\n        if (err) {\n            bail = true\n            callback(err)\n            // future error callbacks will go to error handler\n            callback = err_cb\n        } else if (proxy.count === 0 && !bail) {\n            callback(null, result)\n        }\n    }\n}\n\nfunction noop() {}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/after/index.js\n// module id = 23\n// module chunks = 0", "/*! https://mths.be/utf8js v2.1.2 by @mathias */\n\nvar stringFromCharCode = String.fromCharCode;\n\n// Taken from https://mths.be/punycode\nfunction ucs2decode(string) {\n\tvar output = [];\n\tvar counter = 0;\n\tvar length = string.length;\n\tvar value;\n\tvar extra;\n\twhile (counter < length) {\n\t\tvalue = string.charCodeAt(counter++);\n\t\tif (value >= 0xD800 && value <= 0xDBFF && counter < length) {\n\t\t\t// high surrogate, and there is a next character\n\t\t\textra = string.charCodeAt(counter++);\n\t\t\tif ((extra & 0xFC00) == 0xDC00) { // low surrogate\n\t\t\t\toutput.push(((value & 0x3FF) << 10) + (extra & 0x3FF) + 0x10000);\n\t\t\t} else {\n\t\t\t\t// unmatched surrogate; only append this code unit, in case the next\n\t\t\t\t// code unit is the high surrogate of a surrogate pair\n\t\t\t\toutput.push(value);\n\t\t\t\tcounter--;\n\t\t\t}\n\t\t} else {\n\t\t\toutput.push(value);\n\t\t}\n\t}\n\treturn output;\n}\n\n// Taken from https://mths.be/punycode\nfunction ucs2encode(array) {\n\tvar length = array.length;\n\tvar index = -1;\n\tvar value;\n\tvar output = '';\n\twhile (++index < length) {\n\t\tvalue = array[index];\n\t\tif (value > 0xFFFF) {\n\t\t\tvalue -= 0x10000;\n\t\t\toutput += stringFromCharCode(value >>> 10 & 0x3FF | 0xD800);\n\t\t\tvalue = 0xDC00 | value & 0x3FF;\n\t\t}\n\t\toutput += stringFromCharCode(value);\n\t}\n\treturn output;\n}\n\nfunction checkScalarValue(codePoint, strict) {\n\tif (codePoint >= 0xD800 && codePoint <= 0xDFFF) {\n\t\tif (strict) {\n\t\t\tthrow Error(\n\t\t\t\t'Lone surrogate U+' + codePoint.toString(16).toUpperCase() +\n\t\t\t\t' is not a scalar value'\n\t\t\t);\n\t\t}\n\t\treturn false;\n\t}\n\treturn true;\n}\n/*--------------------------------------------------------------------------*/\n\nfunction createByte(codePoint, shift) {\n\treturn stringFromCharCode(((codePoint >> shift) & 0x3F) | 0x80);\n}\n\nfunction encodeCodePoint(codePoint, strict) {\n\tif ((codePoint & 0xFFFFFF80) == 0) { // 1-byte sequence\n\t\treturn stringFromCharCode(codePoint);\n\t}\n\tvar symbol = '';\n\tif ((codePoint & 0xFFFFF800) == 0) { // 2-byte sequence\n\t\tsymbol = stringFromCharCode(((codePoint >> 6) & 0x1F) | 0xC0);\n\t}\n\telse if ((codePoint & 0xFFFF0000) == 0) { // 3-byte sequence\n\t\tif (!checkScalarValue(codePoint, strict)) {\n\t\t\tcodePoint = 0xFFFD;\n\t\t}\n\t\tsymbol = stringFromCharCode(((codePoint >> 12) & 0x0F) | 0xE0);\n\t\tsymbol += createByte(codePoint, 6);\n\t}\n\telse if ((codePoint & 0xFFE00000) == 0) { // 4-byte sequence\n\t\tsymbol = stringFromCharCode(((codePoint >> 18) & 0x07) | 0xF0);\n\t\tsymbol += createByte(codePoint, 12);\n\t\tsymbol += createByte(codePoint, 6);\n\t}\n\tsymbol += stringFromCharCode((codePoint & 0x3F) | 0x80);\n\treturn symbol;\n}\n\nfunction utf8encode(string, opts) {\n\topts = opts || {};\n\tvar strict = false !== opts.strict;\n\n\tvar codePoints = ucs2decode(string);\n\tvar length = codePoints.length;\n\tvar index = -1;\n\tvar codePoint;\n\tvar byteString = '';\n\twhile (++index < length) {\n\t\tcodePoint = codePoints[index];\n\t\tbyteString += encodeCodePoint(codePoint, strict);\n\t}\n\treturn byteString;\n}\n\n/*--------------------------------------------------------------------------*/\n\nfunction readContinuationByte() {\n\tif (byteIndex >= byteCount) {\n\t\tthrow Error('Invalid byte index');\n\t}\n\n\tvar continuationByte = byteArray[byteIndex] & 0xFF;\n\tbyteIndex++;\n\n\tif ((continuationByte & 0xC0) == 0x80) {\n\t\treturn continuationByte & 0x3F;\n\t}\n\n\t// If we end up here, it’s not a continuation byte\n\tthrow Error('Invalid continuation byte');\n}\n\nfunction decodeSymbol(strict) {\n\tvar byte1;\n\tvar byte2;\n\tvar byte3;\n\tvar byte4;\n\tvar codePoint;\n\n\tif (byteIndex > byteCount) {\n\t\tthrow Error('Invalid byte index');\n\t}\n\n\tif (byteIndex == byteCount) {\n\t\treturn false;\n\t}\n\n\t// Read first byte\n\tbyte1 = byteArray[byteIndex] & 0xFF;\n\tbyteIndex++;\n\n\t// 1-byte sequence (no continuation bytes)\n\tif ((byte1 & 0x80) == 0) {\n\t\treturn byte1;\n\t}\n\n\t// 2-byte sequence\n\tif ((byte1 & 0xE0) == 0xC0) {\n\t\tbyte2 = readContinuationByte();\n\t\tcodePoint = ((byte1 & 0x1F) << 6) | byte2;\n\t\tif (codePoint >= 0x80) {\n\t\t\treturn codePoint;\n\t\t} else {\n\t\t\tthrow Error('Invalid continuation byte');\n\t\t}\n\t}\n\n\t// 3-byte sequence (may include unpaired surrogates)\n\tif ((byte1 & 0xF0) == 0xE0) {\n\t\tbyte2 = readContinuationByte();\n\t\tbyte3 = readContinuationByte();\n\t\tcodePoint = ((byte1 & 0x0F) << 12) | (byte2 << 6) | byte3;\n\t\tif (codePoint >= 0x0800) {\n\t\t\treturn checkScalarValue(codePoint, strict) ? codePoint : 0xFFFD;\n\t\t} else {\n\t\t\tthrow Error('Invalid continuation byte');\n\t\t}\n\t}\n\n\t// 4-byte sequence\n\tif ((byte1 & 0xF8) == 0xF0) {\n\t\tbyte2 = readContinuationByte();\n\t\tbyte3 = readContinuationByte();\n\t\tbyte4 = readContinuationByte();\n\t\tcodePoint = ((byte1 & 0x07) << 0x12) | (byte2 << 0x0C) |\n\t\t\t(byte3 << 0x06) | byte4;\n\t\tif (codePoint >= 0x010000 && codePoint <= 0x10FFFF) {\n\t\t\treturn codePoint;\n\t\t}\n\t}\n\n\tthrow Error('Invalid UTF-8 detected');\n}\n\nvar byteArray;\nvar byteCount;\nvar byteIndex;\nfunction utf8decode(byteString, opts) {\n\topts = opts || {};\n\tvar strict = false !== opts.strict;\n\n\tbyteArray = ucs2decode(byteString);\n\tbyteCount = byteArray.length;\n\tbyteIndex = 0;\n\tvar codePoints = [];\n\tvar tmp;\n\twhile ((tmp = decodeSymbol(strict)) !== false) {\n\t\tcodePoints.push(tmp);\n\t}\n\treturn ucs2encode(codePoints);\n}\n\nmodule.exports = {\n\tversion: '2.1.2',\n\tencode: utf8encode,\n\tdecode: utf8decode\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-parser/lib/utf8.js\n// module id = 24\n// module chunks = 0", "/*\n * base64-arraybuffer\n * https://github.com/niklasvh/base64-arraybuffer\n *\n * Copyright (c) 2012 <PERSON><PERSON>\n * Licensed under the MIT license.\n */\n(function(chars){\n  \"use strict\";\n\n  exports.encode = function(arraybuffer) {\n    var bytes = new Uint8Array(arraybuffer),\n    i, len = bytes.length, base64 = \"\";\n\n    for (i = 0; i < len; i+=3) {\n      base64 += chars[bytes[i] >> 2];\n      base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n      base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n      base64 += chars[bytes[i + 2] & 63];\n    }\n\n    if ((len % 3) === 2) {\n      base64 = base64.substring(0, base64.length - 1) + \"=\";\n    } else if (len % 3 === 1) {\n      base64 = base64.substring(0, base64.length - 2) + \"==\";\n    }\n\n    return base64;\n  };\n\n  exports.decode =  function(base64) {\n    var bufferLength = base64.length * 0.75,\n    len = base64.length, i, p = 0,\n    encoded1, encoded2, encoded3, encoded4;\n\n    if (base64[base64.length - 1] === \"=\") {\n      bufferLength--;\n      if (base64[base64.length - 2] === \"=\") {\n        bufferLength--;\n      }\n    }\n\n    var arraybuffer = new ArrayBuffer(bufferLength),\n    bytes = new Uint8Array(arraybuffer);\n\n    for (i = 0; i < len; i+=4) {\n      encoded1 = chars.indexOf(base64[i]);\n      encoded2 = chars.indexOf(base64[i+1]);\n      encoded3 = chars.indexOf(base64[i+2]);\n      encoded4 = chars.indexOf(base64[i+3]);\n\n      bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n      bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n      bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n\n    return arraybuffer;\n  };\n})(\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\");\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-parser/~/base64-arraybuffer/lib/base64-arraybuffer.js\n// module id = 25\n// module chunks = 0", "/**\r\n * Create a blob builder even when vendor prefixes exist\r\n */\r\n\r\nvar BlobBuilder = typeof BlobBuilder !== 'undefined' ? BlobBuilder :\r\n  typeof WebKitBlobBuilder !== 'undefined' ? WebKitBlobBuilder :\r\n  typeof MSBlobBuilder !== 'undefined' ? MSBlobBuilder :\r\n  typeof MozBlobBuilder !== 'undefined' ? MozBlobBuilder : \r\n  false;\r\n\r\n/**\r\n * Check if Blob constructor is supported\r\n */\r\n\r\nvar blobSupported = (function() {\r\n  try {\r\n    var a = new Blob(['hi']);\r\n    return a.size === 2;\r\n  } catch(e) {\r\n    return false;\r\n  }\r\n})();\r\n\r\n/**\r\n * Check if Blob constructor supports ArrayBufferViews\r\n * Fails in Safari 6, so we need to map to ArrayBuffers there.\r\n */\r\n\r\nvar blobSupportsArrayBufferView = blobSupported && (function() {\r\n  try {\r\n    var b = new Blob([new Uint8Array([1,2])]);\r\n    return b.size === 2;\r\n  } catch(e) {\r\n    return false;\r\n  }\r\n})();\r\n\r\n/**\r\n * Check if BlobBuilder is supported\r\n */\r\n\r\nvar blobBuilderSupported = BlobBuilder\r\n  && BlobBuilder.prototype.append\r\n  && BlobBuilder.prototype.getBlob;\r\n\r\n/**\r\n * Helper function that maps ArrayBufferViews to ArrayBuffers\r\n * Used by BlobBuilder constructor and old browsers that didn't\r\n * support it in the Blob constructor.\r\n */\r\n\r\nfunction mapArrayBufferViews(ary) {\r\n  return ary.map(function(chunk) {\r\n    if (chunk.buffer instanceof ArrayBuffer) {\r\n      var buf = chunk.buffer;\r\n\r\n      // if this is a subarray, make a copy so we only\r\n      // include the subarray region from the underlying buffer\r\n      if (chunk.byteLength !== buf.byteLength) {\r\n        var copy = new Uint8Array(chunk.byteLength);\r\n        copy.set(new Uint8Array(buf, chunk.byteOffset, chunk.byteLength));\r\n        buf = copy.buffer;\r\n      }\r\n\r\n      return buf;\r\n    }\r\n\r\n    return chunk;\r\n  });\r\n}\r\n\r\nfunction BlobBuilderConstructor(ary, options) {\r\n  options = options || {};\r\n\r\n  var bb = new BlobBuilder();\r\n  mapArrayBufferViews(ary).forEach(function(part) {\r\n    bb.append(part);\r\n  });\r\n\r\n  return (options.type) ? bb.getBlob(options.type) : bb.getBlob();\r\n};\r\n\r\nfunction BlobConstructor(ary, options) {\r\n  return new Blob(mapArrayBufferViews(ary), options || {});\r\n};\r\n\r\nif (typeof Blob !== 'undefined') {\r\n  BlobBuilderConstructor.prototype = Blob.prototype;\r\n  BlobConstructor.prototype = Blob.prototype;\r\n}\r\n\r\nmodule.exports = (function() {\r\n  if (blobSupported) {\r\n    return blobSupportsArrayBufferView ? Blob : BlobConstructor;\r\n  } else if (blobBuilderSupported) {\r\n    return BlobBuilderConstructor;\r\n  } else {\r\n    return undefined;\r\n  }\r\n})();\r\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/blob/index.js\n// module id = 26\n// module chunks = 0", "/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\n\nexports.encode = function (obj) {\n  var str = '';\n\n  for (var i in obj) {\n    if (obj.hasOwnProperty(i)) {\n      if (str.length) str += '&';\n      str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n    }\n  }\n\n  return str;\n};\n\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\n\nexports.decode = function(qs){\n  var qry = {};\n  var pairs = qs.split('&');\n  for (var i = 0, l = pairs.length; i < l; i++) {\n    var pair = pairs[i].split('=');\n    qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n  }\n  return qry;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/parseqs/index.js\n// module id = 27\n// module chunks = 0", "\nmodule.exports = function(a, b){\n  var fn = function(){};\n  fn.prototype = b.prototype;\n  a.prototype = new fn;\n  a.prototype.constructor = a;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/component-inherit/index.js\n// module id = 28\n// module chunks = 0", "'use strict';\n\nvar alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_'.split('')\n  , length = 64\n  , map = {}\n  , seed = 0\n  , i = 0\n  , prev;\n\n/**\n * Return a string representing the specified number.\n *\n * @param {Number} num The number to convert.\n * @returns {String} The string representation of the number.\n * @api public\n */\nfunction encode(num) {\n  var encoded = '';\n\n  do {\n    encoded = alphabet[num % length] + encoded;\n    num = Math.floor(num / length);\n  } while (num > 0);\n\n  return encoded;\n}\n\n/**\n * Return the integer value specified by the given string.\n *\n * @param {String} str The string to convert.\n * @returns {Number} The integer value represented by the string.\n * @api public\n */\nfunction decode(str) {\n  var decoded = 0;\n\n  for (i = 0; i < str.length; i++) {\n    decoded = decoded * length + map[str.charAt(i)];\n  }\n\n  return decoded;\n}\n\n/**\n * Yeast: A tiny growing id generator.\n *\n * @returns {String} A unique id.\n * @api public\n */\nfunction yeast() {\n  var now = encode(+new Date());\n\n  if (now !== prev) return seed = 0, prev = now;\n  return now +'.'+ encode(seed++);\n}\n\n//\n// Map each character to its index.\n//\nfor (; i < length; i++) map[alphabet[i]] = i;\n\n//\n// Expose the `yeast`, `encode` and `decode` functions.\n//\nyeast.encode = encode;\nyeast.decode = decode;\nmodule.exports = yeast;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/yeast/index.js\n// module id = 29\n// module chunks = 0", "/**\n * Module requirements.\n */\n\nvar Polling = require('./polling');\nvar inherit = require('component-inherit');\nvar globalThis = require('../globalThis');\n\n/**\n * Module exports.\n */\n\nmodule.exports = JSONPPolling;\n\n/**\n * Cached regular expressions.\n */\n\nvar rNewline = /\\n/g;\nvar rEscapedNewline = /\\\\n/g;\n\n/**\n * Global JSONP callbacks.\n */\n\nvar callbacks;\n\n/**\n * Noop.\n */\n\nfunction empty () { }\n\n/**\n * JSONP Polling constructor.\n *\n * @param {Object} opts.\n * @api public\n */\n\nfunction JSONPPolling (opts) {\n  Polling.call(this, opts);\n\n  this.query = this.query || {};\n\n  // define global callbacks array if not present\n  // we do this here (lazily) to avoid unneeded global pollution\n  if (!callbacks) {\n    // we need to consider multiple engines in the same page\n    callbacks = globalThis.___eio = (globalThis.___eio || []);\n  }\n\n  // callback identifier\n  this.index = callbacks.length;\n\n  // add callback to jsonp global\n  var self = this;\n  callbacks.push(function (msg) {\n    self.onData(msg);\n  });\n\n  // append to query string\n  this.query.j = this.index;\n\n  // prevent spurious errors from being emitted when the window is unloaded\n  if (typeof addEventListener === 'function') {\n    addEventListener('beforeunload', function () {\n      if (self.script) self.script.onerror = empty;\n    }, false);\n  }\n}\n\n/**\n * Inherits from Polling.\n */\n\ninherit(JSONPPolling, Polling);\n\n/*\n * JSONP only supports binary as base64 encoded strings\n */\n\nJSONPPolling.prototype.supportsBinary = false;\n\n/**\n * Closes the socket.\n *\n * @api private\n */\n\nJSONPPolling.prototype.doClose = function () {\n  if (this.script) {\n    this.script.parentNode.removeChild(this.script);\n    this.script = null;\n  }\n\n  if (this.form) {\n    this.form.parentNode.removeChild(this.form);\n    this.form = null;\n    this.iframe = null;\n  }\n\n  Polling.prototype.doClose.call(this);\n};\n\n/**\n * Starts a poll cycle.\n *\n * @api private\n */\n\nJSONPPolling.prototype.doPoll = function () {\n  var self = this;\n  var script = document.createElement('script');\n\n  if (this.script) {\n    this.script.parentNode.removeChild(this.script);\n    this.script = null;\n  }\n\n  script.async = true;\n  script.src = this.uri();\n  script.onerror = function (e) {\n    self.onError('jsonp poll error', e);\n  };\n\n  var insertAt = document.getElementsByTagName('script')[0];\n  if (insertAt) {\n    insertAt.parentNode.insertBefore(script, insertAt);\n  } else {\n    (document.head || document.body).appendChild(script);\n  }\n  this.script = script;\n\n  var isUAgecko = 'undefined' !== typeof navigator && /gecko/i.test(navigator.userAgent);\n\n  if (isUAgecko) {\n    setTimeout(function () {\n      var iframe = document.createElement('iframe');\n      document.body.appendChild(iframe);\n      document.body.removeChild(iframe);\n    }, 100);\n  }\n};\n\n/**\n * Writes with a hidden iframe.\n *\n * @param {String} data to send\n * @param {Function} called upon flush.\n * @api private\n */\n\nJSONPPolling.prototype.doWrite = function (data, fn) {\n  var self = this;\n\n  if (!this.form) {\n    var form = document.createElement('form');\n    var area = document.createElement('textarea');\n    var id = this.iframeId = 'eio_iframe_' + this.index;\n    var iframe;\n\n    form.className = 'socketio';\n    form.style.position = 'absolute';\n    form.style.top = '-1000px';\n    form.style.left = '-1000px';\n    form.target = id;\n    form.method = 'POST';\n    form.setAttribute('accept-charset', 'utf-8');\n    area.name = 'd';\n    form.appendChild(area);\n    document.body.appendChild(form);\n\n    this.form = form;\n    this.area = area;\n  }\n\n  this.form.action = this.uri();\n\n  function complete () {\n    initIframe();\n    fn();\n  }\n\n  function initIframe () {\n    if (self.iframe) {\n      try {\n        self.form.removeChild(self.iframe);\n      } catch (e) {\n        self.onError('jsonp polling iframe removal error', e);\n      }\n    }\n\n    try {\n      // ie6 dynamic iframes with target=\"\" support (thanks Chris Lambacher)\n      var html = '<iframe src=\"javascript:0\" name=\"' + self.iframeId + '\">';\n      iframe = document.createElement(html);\n    } catch (e) {\n      iframe = document.createElement('iframe');\n      iframe.name = self.iframeId;\n      iframe.src = 'javascript:0';\n    }\n\n    iframe.id = self.iframeId;\n\n    self.form.appendChild(iframe);\n    self.iframe = iframe;\n  }\n\n  initIframe();\n\n  // escape \\n to prevent it from being converted into \\r\\n by some UAs\n  // double escaping is required for escaped new lines because unescaping of new lines can be done safely on server-side\n  data = data.replace(rEscapedNewline, '\\\\\\n');\n  this.area.value = data.replace(rNewline, '\\\\n');\n\n  try {\n    this.form.submit();\n  } catch (e) {}\n\n  if (this.iframe.attachEvent) {\n    this.iframe.onreadystatechange = function () {\n      if (self.iframe.readyState === 'complete') {\n        complete();\n      }\n    };\n  } else {\n    this.iframe.onload = complete;\n  }\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/transports/polling-jsonp.js\n// module id = 30\n// module chunks = 0", "/**\n * Module dependencies.\n */\n\nvar Transport = require('../transport');\nvar parser = require('engine.io-parser');\nvar parseqs = require('parseqs');\nvar inherit = require('component-inherit');\nvar yeast = require('yeast');\nvar debug = require('debug')('engine.io-client:websocket');\n\nvar BrowserWebSocket, NodeWebSocket;\n\nif (typeof WebSocket !== 'undefined') {\n  BrowserWebSocket = WebSocket;\n} else if (typeof self !== 'undefined') {\n  BrowserWebSocket = self.WebSocket || self.MozWebSocket;\n}\n\nif (typeof window === 'undefined') {\n  try {\n    NodeWebSocket = require('ws');\n  } catch (e) { }\n}\n\n/**\n * Get either the `WebSocket` or `MozWebSocket` globals\n * in the browser or try to resolve WebSocket-compatible\n * interface exposed by `ws` for Node-like environment.\n */\n\nvar WebSocketImpl = BrowserWebSocket || NodeWebSocket;\n\n/**\n * Module exports.\n */\n\nmodule.exports = WS;\n\n/**\n * WebSocket transport constructor.\n *\n * @api {Object} connection options\n * @api public\n */\n\nfunction WS (opts) {\n  var forceBase64 = (opts && opts.forceBase64);\n  if (forceBase64) {\n    this.supportsBinary = false;\n  }\n  this.perMessageDeflate = opts.perMessageDeflate;\n  this.usingBrowserWebSocket = BrowserWebSocket && !opts.forceNode;\n  this.protocols = opts.protocols;\n  if (!this.usingBrowserWebSocket) {\n    WebSocketImpl = NodeWebSocket;\n  }\n  Transport.call(this, opts);\n}\n\n/**\n * Inherits from Transport.\n */\n\ninherit(WS, Transport);\n\n/**\n * Transport name.\n *\n * @api public\n */\n\nWS.prototype.name = 'websocket';\n\n/*\n * WebSockets support binary\n */\n\nWS.prototype.supportsBinary = true;\n\n/**\n * Opens socket.\n *\n * @api private\n */\n\nWS.prototype.doOpen = function () {\n  if (!this.check()) {\n    // let probe timeout\n    return;\n  }\n\n  var uri = this.uri();\n  var protocols = this.protocols;\n\n  var opts = {};\n\n  if (!this.isReactNative) {\n    opts.agent = this.agent;\n    opts.perMessageDeflate = this.perMessageDeflate;\n\n    // SSL options for Node.js client\n    opts.pfx = this.pfx;\n    opts.key = this.key;\n    opts.passphrase = this.passphrase;\n    opts.cert = this.cert;\n    opts.ca = this.ca;\n    opts.ciphers = this.ciphers;\n    opts.rejectUnauthorized = this.rejectUnauthorized;\n  }\n\n  if (this.extraHeaders) {\n    opts.headers = this.extraHeaders;\n  }\n  if (this.localAddress) {\n    opts.localAddress = this.localAddress;\n  }\n\n  try {\n    this.ws =\n      this.usingBrowserWebSocket && !this.isReactNative\n        ? protocols\n          ? new WebSocketImpl(uri, protocols)\n          : new WebSocketImpl(uri)\n        : new WebSocketImpl(uri, protocols, opts);\n  } catch (err) {\n    return this.emit('error', err);\n  }\n\n  if (this.ws.binaryType === undefined) {\n    this.supportsBinary = false;\n  }\n\n  if (this.ws.supports && this.ws.supports.binary) {\n    this.supportsBinary = true;\n    this.ws.binaryType = 'nodebuffer';\n  } else {\n    this.ws.binaryType = 'arraybuffer';\n  }\n\n  this.addEventListeners();\n};\n\n/**\n * Adds event listeners to the socket\n *\n * @api private\n */\n\nWS.prototype.addEventListeners = function () {\n  var self = this;\n\n  this.ws.onopen = function () {\n    self.onOpen();\n  };\n  this.ws.onclose = function () {\n    self.onClose();\n  };\n  this.ws.onmessage = function (ev) {\n    self.onData(ev.data);\n  };\n  this.ws.onerror = function (e) {\n    self.onError('websocket error', e);\n  };\n};\n\n/**\n * Writes data to socket.\n *\n * @param {Array} array of packets.\n * @api private\n */\n\nWS.prototype.write = function (packets) {\n  var self = this;\n  this.writable = false;\n\n  // encodePacket efficient as it uses WS framing\n  // no need for encodePayload\n  var total = packets.length;\n  for (var i = 0, l = total; i < l; i++) {\n    (function (packet) {\n      parser.encodePacket(packet, self.supportsBinary, function (data) {\n        if (!self.usingBrowserWebSocket) {\n          // always create a new object (GH-437)\n          var opts = {};\n          if (packet.options) {\n            opts.compress = packet.options.compress;\n          }\n\n          if (self.perMessageDeflate) {\n            var len = 'string' === typeof data ? Buffer.byteLength(data) : data.length;\n            if (len < self.perMessageDeflate.threshold) {\n              opts.compress = false;\n            }\n          }\n        }\n\n        // Sometimes the websocket has already been closed but the browser didn't\n        // have a chance of informing us about it yet, in that case send will\n        // throw an error\n        try {\n          if (self.usingBrowserWebSocket) {\n            // TypeError is thrown when passing the second argument on Safari\n            self.ws.send(data);\n          } else {\n            self.ws.send(data, opts);\n          }\n        } catch (e) {\n\n        }\n\n        --total || done();\n      });\n    })(packets[i]);\n  }\n\n  function done () {\n    self.emit('flush');\n\n    // fake drain\n    // defer to next tick to allow Socket to clear writeBuffer\n    setTimeout(function () {\n      self.writable = true;\n      self.emit('drain');\n    }, 0);\n  }\n};\n\n/**\n * Called upon close\n *\n * @api private\n */\n\nWS.prototype.onClose = function () {\n  Transport.prototype.onClose.call(this);\n};\n\n/**\n * Closes socket.\n *\n * @api private\n */\n\nWS.prototype.doClose = function () {\n  if (typeof this.ws !== 'undefined') {\n    this.ws.close();\n  }\n};\n\n/**\n * Generates uri for connection.\n *\n * @api private\n */\n\nWS.prototype.uri = function () {\n  var query = this.query || {};\n  var schema = this.secure ? 'wss' : 'ws';\n  var port = '';\n\n  // avoid port if default for schema\n  if (this.port && (('wss' === schema && Number(this.port) !== 443) ||\n    ('ws' === schema && Number(this.port) !== 80))) {\n    port = ':' + this.port;\n  }\n\n  // append timestamp to URI\n  if (this.timestampRequests) {\n    query[this.timestampParam] = yeast();\n  }\n\n  // communicate binary support capabilities\n  if (!this.supportsBinary) {\n    query.b64 = 1;\n  }\n\n  query = parseqs.encode(query);\n\n  // prepend ? to query\n  if (query.length) {\n    query = '?' + query;\n  }\n\n  var ipv6 = this.hostname.indexOf(':') !== -1;\n  return schema + '://' + (ipv6 ? '[' + this.hostname + ']' : this.hostname) + port + this.path + query;\n};\n\n/**\n * Feature detection for WebSocket.\n *\n * @return {Boolean} whether this transport is available.\n * @api public\n */\n\nWS.prototype.check = function () {\n  return !!WebSocketImpl && !('__initialize' in WebSocketImpl && this.name === WS.prototype.name);\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/transports/websocket.js\n// module id = 31\n// module chunks = 0", "/* (ignored) */\n\n\n//////////////////\n// WEBPACK FOOTER\n// ws (ignored)\n// module id = 32\n// module chunks = 0", "\nvar indexOf = [].indexOf;\n\nmodule.exports = function(arr, obj){\n  if (indexOf) return arr.indexOf(obj);\n  for (var i = 0; i < arr.length; ++i) {\n    if (arr[i] === obj) return i;\n  }\n  return -1;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/indexof/index.js\n// module id = 33\n// module chunks = 0", "\n/**\n * Module dependencies.\n */\n\nvar parser = require('socket.io-parser');\nvar Emitter = require('component-emitter');\nvar toArray = require('to-array');\nvar on = require('./on');\nvar bind = require('component-bind');\nvar debug = require('debug')('socket.io-client:socket');\nvar parseqs = require('parseqs');\nvar hasBin = require('has-binary2');\n\n/**\n * Module exports.\n */\n\nmodule.exports = exports = Socket;\n\n/**\n * Internal events (blacklisted).\n * These events can't be emitted by the user.\n *\n * @api private\n */\n\nvar events = {\n  connect: 1,\n  connect_error: 1,\n  connect_timeout: 1,\n  connecting: 1,\n  disconnect: 1,\n  error: 1,\n  reconnect: 1,\n  reconnect_attempt: 1,\n  reconnect_failed: 1,\n  reconnect_error: 1,\n  reconnecting: 1,\n  ping: 1,\n  pong: 1\n};\n\n/**\n * Shortcut to `Emitter#emit`.\n */\n\nvar emit = Emitter.prototype.emit;\n\n/**\n * `Socket` constructor.\n *\n * @api public\n */\n\nfunction Socket (io, nsp, opts) {\n  this.io = io;\n  this.nsp = nsp;\n  this.json = this; // compat\n  this.ids = 0;\n  this.acks = {};\n  this.receiveBuffer = [];\n  this.sendBuffer = [];\n  this.connected = false;\n  this.disconnected = true;\n  this.flags = {};\n  if (opts && opts.query) {\n    this.query = opts.query;\n  }\n  if (this.io.autoConnect) this.open();\n}\n\n/**\n * Mix in `Emitter`.\n */\n\nEmitter(Socket.prototype);\n\n/**\n * Subscribe to open, close and packet events\n *\n * @api private\n */\n\nSocket.prototype.subEvents = function () {\n  if (this.subs) return;\n\n  var io = this.io;\n  this.subs = [\n    on(io, 'open', bind(this, 'onopen')),\n    on(io, 'packet', bind(this, 'onpacket')),\n    on(io, 'close', bind(this, 'onclose'))\n  ];\n};\n\n/**\n * \"Opens\" the socket.\n *\n * @api public\n */\n\nSocket.prototype.open =\nSocket.prototype.connect = function () {\n  if (this.connected) return this;\n\n  this.subEvents();\n  if (!this.io.reconnecting) this.io.open(); // ensure open\n  if ('open' === this.io.readyState) this.onopen();\n  this.emit('connecting');\n  return this;\n};\n\n/**\n * Sends a `message` event.\n *\n * @return {Socket} self\n * @api public\n */\n\nSocket.prototype.send = function () {\n  var args = toArray(arguments);\n  args.unshift('message');\n  this.emit.apply(this, args);\n  return this;\n};\n\n/**\n * Override `emit`.\n * If the event is in `events`, it's emitted normally.\n *\n * @param {String} event name\n * @return {Socket} self\n * @api public\n */\n\nSocket.prototype.emit = function (ev) {\n  if (events.hasOwnProperty(ev)) {\n    emit.apply(this, arguments);\n    return this;\n  }\n\n  var args = toArray(arguments);\n  var packet = {\n    type: (this.flags.binary !== undefined ? this.flags.binary : hasBin(args)) ? parser.BINARY_EVENT : parser.EVENT,\n    data: args\n  };\n\n  packet.options = {};\n  packet.options.compress = !this.flags || false !== this.flags.compress;\n\n  // event ack callback\n  if ('function' === typeof args[args.length - 1]) {\n\n    this.acks[this.ids] = args.pop();\n    packet.id = this.ids++;\n  }\n\n  if (this.connected) {\n    this.packet(packet);\n  } else {\n    this.sendBuffer.push(packet);\n  }\n\n  this.flags = {};\n\n  return this;\n};\n\n/**\n * Sends a packet.\n *\n * @param {Object} packet\n * @api private\n */\n\nSocket.prototype.packet = function (packet) {\n  packet.nsp = this.nsp;\n  this.io.packet(packet);\n};\n\n/**\n * Called upon engine `open`.\n *\n * @api private\n */\n\nSocket.prototype.onopen = function () {\n\n\n  // write connect packet if necessary\n  if ('/' !== this.nsp) {\n    if (this.query) {\n      var query = typeof this.query === 'object' ? parseqs.encode(this.query) : this.query;\n\n      this.packet({type: parser.CONNECT, query: query});\n    } else {\n      this.packet({type: parser.CONNECT});\n    }\n  }\n};\n\n/**\n * Called upon engine `close`.\n *\n * @param {String} reason\n * @api private\n */\n\nSocket.prototype.onclose = function (reason) {\n\n  this.connected = false;\n  this.disconnected = true;\n  delete this.id;\n  this.emit('disconnect', reason);\n};\n\n/**\n * Called with socket packet.\n *\n * @param {Object} packet\n * @api private\n */\n\nSocket.prototype.onpacket = function (packet) {\n  var sameNamespace = packet.nsp === this.nsp;\n  var rootNamespaceError = packet.type === parser.ERROR && packet.nsp === '/';\n\n  if (!sameNamespace && !rootNamespaceError) return;\n\n  switch (packet.type) {\n    case parser.CONNECT:\n      this.onconnect();\n      break;\n\n    case parser.EVENT:\n      this.onevent(packet);\n      break;\n\n    case parser.BINARY_EVENT:\n      this.onevent(packet);\n      break;\n\n    case parser.ACK:\n      this.onack(packet);\n      break;\n\n    case parser.BINARY_ACK:\n      this.onack(packet);\n      break;\n\n    case parser.DISCONNECT:\n      this.ondisconnect();\n      break;\n\n    case parser.ERROR:\n      this.emit('error', packet.data);\n      break;\n  }\n};\n\n/**\n * Called upon a server event.\n *\n * @param {Object} packet\n * @api private\n */\n\nSocket.prototype.onevent = function (packet) {\n  var args = packet.data || [];\n\n\n  if (null != packet.id) {\n\n    args.push(this.ack(packet.id));\n  }\n\n  if (this.connected) {\n    emit.apply(this, args);\n  } else {\n    this.receiveBuffer.push(args);\n  }\n};\n\n/**\n * Produces an ack callback to emit with an event.\n *\n * @api private\n */\n\nSocket.prototype.ack = function (id) {\n  var self = this;\n  var sent = false;\n  return function () {\n    // prevent double callbacks\n    if (sent) return;\n    sent = true;\n    var args = toArray(arguments);\n\n\n    self.packet({\n      type: hasBin(args) ? parser.BINARY_ACK : parser.ACK,\n      id: id,\n      data: args\n    });\n  };\n};\n\n/**\n * Called upon a server acknowlegement.\n *\n * @param {Object} packet\n * @api private\n */\n\nSocket.prototype.onack = function (packet) {\n  var ack = this.acks[packet.id];\n  if ('function' === typeof ack) {\n\n    ack.apply(this, packet.data);\n    delete this.acks[packet.id];\n  } else {\n\n  }\n};\n\n/**\n * Called upon server connect.\n *\n * @api private\n */\n\nSocket.prototype.onconnect = function () {\n  this.connected = true;\n  this.disconnected = false;\n  this.emitBuffered();\n  this.emit('connect');\n};\n\n/**\n * Emit buffered events (received and emitted).\n *\n * @api private\n */\n\nSocket.prototype.emitBuffered = function () {\n  var i;\n  for (i = 0; i < this.receiveBuffer.length; i++) {\n    emit.apply(this, this.receiveBuffer[i]);\n  }\n  this.receiveBuffer = [];\n\n  for (i = 0; i < this.sendBuffer.length; i++) {\n    this.packet(this.sendBuffer[i]);\n  }\n  this.sendBuffer = [];\n};\n\n/**\n * Called upon server disconnect.\n *\n * @api private\n */\n\nSocket.prototype.ondisconnect = function () {\n\n  this.destroy();\n  this.onclose('io server disconnect');\n};\n\n/**\n * Called upon forced client/server side disconnections,\n * this method ensures the manager stops tracking us and\n * that reconnections don't get triggered for this.\n *\n * @api private.\n */\n\nSocket.prototype.destroy = function () {\n  if (this.subs) {\n    // clean subscriptions to avoid reconnections\n    for (var i = 0; i < this.subs.length; i++) {\n      this.subs[i].destroy();\n    }\n    this.subs = null;\n  }\n\n  this.io.destroy(this);\n};\n\n/**\n * Disconnects the socket manually.\n *\n * @return {Socket} self\n * @api public\n */\n\nSocket.prototype.close =\nSocket.prototype.disconnect = function () {\n  if (this.connected) {\n\n    this.packet({ type: parser.DISCONNECT });\n  }\n\n  // remove socket from pool\n  this.destroy();\n\n  if (this.connected) {\n    // fire events\n    this.onclose('io client disconnect');\n  }\n  return this;\n};\n\n/**\n * Sets the compress flag.\n *\n * @param {Boolean} if `true`, compresses the sending data\n * @return {Socket} self\n * @api public\n */\n\nSocket.prototype.compress = function (compress) {\n  this.flags.compress = compress;\n  return this;\n};\n\n/**\n * Sets the binary flag\n *\n * @param {Boolean} whether the emitted data contains binary\n * @return {Socket} self\n * @api public\n */\n\nSocket.prototype.binary = function (binary) {\n  this.flags.binary = binary;\n  return this;\n};\n\n\n\n// WEBPACK FOOTER //\n// ./lib/socket.js", "module.exports = toArray\n\nfunction toArray(list, index) {\n    var array = []\n\n    index = index || 0\n\n    for (var i = index || 0; i < list.length; i++) {\n        array[i - index] = list[i]\n    }\n\n    return array\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/to-array/index.js\n// module id = 35\n// module chunks = 0", "\n/**\n * Module exports.\n */\n\nmodule.exports = on;\n\n/**\n * Helper for subscriptions.\n *\n * @param {Object|EventEmitter} obj with `Emitter` mixin or `EventEmitter`\n * @param {String} event name\n * @param {Function} callback\n * @api public\n */\n\nfunction on (obj, ev, fn) {\n  obj.on(ev, fn);\n  return {\n    destroy: function () {\n      obj.removeListener(ev, fn);\n    }\n  };\n}\n\n\n\n// WEBPACK FOOTER //\n// ./lib/on.js", "/**\n * Slice reference.\n */\n\nvar slice = [].slice;\n\n/**\n * Bind `obj` to `fn`.\n *\n * @param {Object} obj\n * @param {Function|String} fn or string\n * @return {Function}\n * @api public\n */\n\nmodule.exports = function(obj, fn){\n  if ('string' == typeof fn) fn = obj[fn];\n  if ('function' != typeof fn) throw new Error('bind() requires a function');\n  var args = slice.call(arguments, 2);\n  return function(){\n    return fn.apply(obj, args.concat(slice.call(arguments)));\n  }\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/component-bind/index.js\n// module id = 37\n// module chunks = 0", "\n/**\n * Expose `Backoff`.\n */\n\nmodule.exports = Backoff;\n\n/**\n * Initialize backoff timer with `opts`.\n *\n * - `min` initial timeout in milliseconds [100]\n * - `max` max timeout [10000]\n * - `jitter` [0]\n * - `factor` [2]\n *\n * @param {Object} opts\n * @api public\n */\n\nfunction Backoff(opts) {\n  opts = opts || {};\n  this.ms = opts.min || 100;\n  this.max = opts.max || 10000;\n  this.factor = opts.factor || 2;\n  this.jitter = opts.jitter > 0 && opts.jitter <= 1 ? opts.jitter : 0;\n  this.attempts = 0;\n}\n\n/**\n * Return the backoff duration.\n *\n * @return {Number}\n * @api public\n */\n\nBackoff.prototype.duration = function(){\n  var ms = this.ms * Math.pow(this.factor, this.attempts++);\n  if (this.jitter) {\n    var rand =  Math.random();\n    var deviation = Math.floor(rand * this.jitter * ms);\n    ms = (Math.floor(rand * 10) & 1) == 0  ? ms - deviation : ms + deviation;\n  }\n  return Math.min(ms, this.max) | 0;\n};\n\n/**\n * Reset the number of attempts.\n *\n * @api public\n */\n\nBackoff.prototype.reset = function(){\n  this.attempts = 0;\n};\n\n/**\n * Set the minimum duration\n *\n * @api public\n */\n\nBackoff.prototype.setMin = function(min){\n  this.ms = min;\n};\n\n/**\n * Set the maximum duration\n *\n * @api public\n */\n\nBackoff.prototype.setMax = function(max){\n  this.max = max;\n};\n\n/**\n * Set the jitter\n *\n * @api public\n */\n\nBackoff.prototype.setJitter = function(jitter){\n  this.jitter = jitter;\n};\n\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/backo2/index.js\n// module id = 38\n// module chunks = 0"], "sourceRoot": ""}