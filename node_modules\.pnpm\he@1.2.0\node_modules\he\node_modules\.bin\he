#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/e/a_c/vscode/cx/dome3/wechatAlliance/node_modules/.pnpm/he@1.2.0/node_modules/he/bin/node_modules:/mnt/e/a_c/vscode/cx/dome3/wechatAlliance/node_modules/.pnpm/he@1.2.0/node_modules/he/node_modules:/mnt/e/a_c/vscode/cx/dome3/wechatAlliance/node_modules/.pnpm/he@1.2.0/node_modules:/mnt/e/a_c/vscode/cx/dome3/wechatAlliance/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/e/a_c/vscode/cx/dome3/wechatAlliance/node_modules/.pnpm/he@1.2.0/node_modules/he/bin/node_modules:/mnt/e/a_c/vscode/cx/dome3/wechatAlliance/node_modules/.pnpm/he@1.2.0/node_modules/he/node_modules:/mnt/e/a_c/vscode/cx/dome3/wechatAlliance/node_modules/.pnpm/he@1.2.0/node_modules:/mnt/e/a_c/vscode/cx/dome3/wechatAlliance/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin/he" "$@"
else
  exec node  "$basedir/../../bin/he" "$@"
fi
