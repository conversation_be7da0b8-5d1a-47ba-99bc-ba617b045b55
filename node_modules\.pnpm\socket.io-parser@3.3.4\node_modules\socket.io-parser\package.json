{"name": "socket.io-parser", "version": "3.3.4", "description": "socket.io protocol parser", "repository": {"type": "git", "url": "https://github.com/Automattic/socket.io-parser.git"}, "files": ["binary.js", "index.js", "is-buffer.js"], "dependencies": {"component-emitter": "~1.3.0", "debug": "~3.1.0", "isarray": "2.0.1"}, "devDependencies": {"benchmark": "2.1.2", "expect.js": "0.3.1", "mocha": "3.2.0", "socket.io-browsers": "^1.0.0", "zuul": "3.11.1", "zuul-ngrok": "4.0.0"}, "scripts": {"test": "make test"}, "license": "MIT"}