{"name": "right-align", "description": "Right-align the text in a string.", "version": "0.1.3", "homepage": "https://github.com/jonschlinkert/right-align", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/right-align.git"}, "bugs": {"url": "https://github.com/jonschlinkert/right-align/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"align-text": "^0.1.1"}, "devDependencies": {"mocha": "*", "should": "*"}, "keywords": ["align", "align-center", "center", "center-align", "right", "right-align", "text", "typography"]}