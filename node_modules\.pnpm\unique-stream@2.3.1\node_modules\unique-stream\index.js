'use strict';

var filter = require('through2-filter').obj;
var stringify = require("json-stable-stringify-without-jsonify");

var ES6Set;
if (typeof global.Set === 'function') {
  ES6Set = global.Set;
} else {
  ES6Set = function() {
    this.keys = [];
    this.has = function(val) {
      return this.keys.indexOf(val) !== -1;
    },
    this.add = function(val) {
      this.keys.push(val);
    }
  }
}

function prop(propName) {
  return function (data) {
    return data[propName];
  };
}

module.exports = unique;
function unique(propName, keyStore) {
  keyStore = keyStore || new ES6Set();

  var keyfn = stringify;
  if (typeof propName === 'string') {
    keyfn = prop(propName);
  } else if (typeof propName === 'function') {
    keyfn = propName;
  }

  return filter(function (data) {
    var key = keyfn(data);

    if (keyStore.has(key)) {
      return false;
    }

    keyStore.add(key);
    return true;
  });
}
