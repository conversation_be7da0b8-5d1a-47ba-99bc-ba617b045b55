hoistPattern:
  - '*'
hoistedDependencies:
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/parser@7.28.3':
    '@babel/parser': private
  '@babel/types@7.28.2':
    '@babel/types': private
  '@types/keyv@3.1.4':
    '@types/keyv': private
  '@types/node@24.3.1':
    '@types/node': private
  '@types/q@1.5.8':
    '@types/q': private
  '@types/responselike@1.0.3':
    '@types/responselike': private
  '@vue/compiler-sfc@2.7.16':
    '@vue/compiler-sfc': private
  abbrev@1.1.1:
    abbrev: private
  accepts@1.3.8:
    accepts: private
  acorn-dynamic-import@2.0.2:
    acorn-dynamic-import: private
  acorn@5.7.4:
    acorn: private
  adjust-sourcemap-loader@1.2.0:
    adjust-sourcemap-loader: private
  after@0.8.2:
    after: private
  ajv-keywords@3.5.2(ajv@6.12.6):
    ajv-keywords: private
  ajv@6.12.6:
    ajv: private
  align-text@0.1.4:
    align-text: private
  alphanum-sort@1.0.2:
    alphanum-sort: private
  amdefine@1.0.1:
    amdefine: private
  ansi-gray@0.1.1:
    ansi-gray: private
  ansi-html@0.0.7:
    ansi-html: private
  ansi-regex@2.1.1:
    ansi-regex: private
  ansi-styles@2.2.1:
    ansi-styles: private
  ansi-wrap@0.1.0:
    ansi-wrap: private
  anymatch@1.3.2:
    anymatch: private
  aproba@1.2.0:
    aproba: private
  archive-type@3.2.0:
    archive-type: private
  are-we-there-yet@1.1.7:
    are-we-there-yet: private
  argparse@1.0.10:
    argparse: private
  arr-diff@2.0.0:
    arr-diff: private
  arr-flatten@1.1.0:
    arr-flatten: private
  arr-union@3.1.0:
    arr-union: private
  array-buffer-byte-length@1.0.2:
    array-buffer-byte-length: private
  array-differ@1.0.0:
    array-differ: private
  array-find-index@1.0.2:
    array-find-index: private
  array-flatten@2.1.2:
    array-flatten: private
  array-includes@3.1.9:
    array-includes: private
  array-union@1.0.2:
    array-union: private
  array-uniq@1.0.3:
    array-uniq: private
  array-unique@0.3.2:
    array-unique: private
  array.prototype.reduce@1.0.8:
    array.prototype.reduce: private
  arraybuffer.prototype.slice@1.0.4:
    arraybuffer.prototype.slice: private
  arraybuffer.slice@0.0.7:
    arraybuffer.slice: private
  asn1.js@4.10.1:
    asn1.js: private
  asn1@0.2.6:
    asn1: private
  assert-plus@1.0.0:
    assert-plus: private
  assert@1.5.1:
    assert: private
  assign-symbols@1.0.0:
    assign-symbols: private
  ast-types@0.9.6:
    ast-types: private
  async-each-series@1.1.0:
    async-each-series: private
  async-each@1.0.6:
    async-each: private
  async-foreach@0.1.3:
    async-foreach: private
  async-function@1.0.0:
    async-function: private
  async@2.6.4:
    async: private
  asynckit@0.4.0:
    asynckit: private
  atob@2.1.2:
    atob: private
  autoprefixer@7.2.6:
    autoprefixer: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  aws-sign2@0.7.0:
    aws-sign2: private
  aws4@1.13.2:
    aws4: private
  babel-code-frame@6.26.0:
    babel-code-frame: private
  babel-core@6.26.3:
    babel-core: private
  babel-generator@6.26.1:
    babel-generator: private
  babel-helper-builder-binary-assignment-operator-visitor@6.24.1:
    babel-helper-builder-binary-assignment-operator-visitor: private
  babel-helper-call-delegate@6.24.1:
    babel-helper-call-delegate: private
  babel-helper-define-map@6.26.0:
    babel-helper-define-map: private
  babel-helper-explode-assignable-expression@6.24.1:
    babel-helper-explode-assignable-expression: private
  babel-helper-function-name@6.24.1:
    babel-helper-function-name: private
  babel-helper-get-function-arity@6.24.1:
    babel-helper-get-function-arity: private
  babel-helper-hoist-variables@6.24.1:
    babel-helper-hoist-variables: private
  babel-helper-optimise-call-expression@6.24.1:
    babel-helper-optimise-call-expression: private
  babel-helper-regex@6.26.0:
    babel-helper-regex: private
  babel-helper-remap-async-to-generator@6.24.1:
    babel-helper-remap-async-to-generator: private
  babel-helper-replace-supers@6.24.1:
    babel-helper-replace-supers: private
  babel-helpers@6.24.1:
    babel-helpers: private
  babel-loader@7.1.5(babel-core@6.26.3)(webpack@3.12.0):
    babel-loader: private
  babel-messages@6.23.0:
    babel-messages: private
  babel-plugin-check-es2015-constants@6.22.0:
    babel-plugin-check-es2015-constants: private
  babel-plugin-syntax-async-functions@6.13.0:
    babel-plugin-syntax-async-functions: private
  babel-plugin-syntax-exponentiation-operator@6.13.0:
    babel-plugin-syntax-exponentiation-operator: private
  babel-plugin-syntax-object-rest-spread@6.13.0:
    babel-plugin-syntax-object-rest-spread: private
  babel-plugin-syntax-trailing-function-commas@6.22.0:
    babel-plugin-syntax-trailing-function-commas: private
  babel-plugin-transform-async-to-generator@6.24.1:
    babel-plugin-transform-async-to-generator: private
  babel-plugin-transform-es2015-arrow-functions@6.22.0:
    babel-plugin-transform-es2015-arrow-functions: private
  babel-plugin-transform-es2015-block-scoped-functions@6.22.0:
    babel-plugin-transform-es2015-block-scoped-functions: private
  babel-plugin-transform-es2015-block-scoping@6.26.0:
    babel-plugin-transform-es2015-block-scoping: private
  babel-plugin-transform-es2015-classes@6.24.1:
    babel-plugin-transform-es2015-classes: private
  babel-plugin-transform-es2015-computed-properties@6.24.1:
    babel-plugin-transform-es2015-computed-properties: private
  babel-plugin-transform-es2015-destructuring@6.23.0:
    babel-plugin-transform-es2015-destructuring: private
  babel-plugin-transform-es2015-duplicate-keys@6.24.1:
    babel-plugin-transform-es2015-duplicate-keys: private
  babel-plugin-transform-es2015-for-of@6.23.0:
    babel-plugin-transform-es2015-for-of: private
  babel-plugin-transform-es2015-function-name@6.24.1:
    babel-plugin-transform-es2015-function-name: private
  babel-plugin-transform-es2015-literals@6.22.0:
    babel-plugin-transform-es2015-literals: private
  babel-plugin-transform-es2015-modules-amd@6.24.1:
    babel-plugin-transform-es2015-modules-amd: private
  babel-plugin-transform-es2015-modules-commonjs@6.26.2:
    babel-plugin-transform-es2015-modules-commonjs: private
  babel-plugin-transform-es2015-modules-systemjs@6.24.1:
    babel-plugin-transform-es2015-modules-systemjs: private
  babel-plugin-transform-es2015-modules-umd@6.24.1:
    babel-plugin-transform-es2015-modules-umd: private
  babel-plugin-transform-es2015-object-super@6.24.1:
    babel-plugin-transform-es2015-object-super: private
  babel-plugin-transform-es2015-parameters@6.24.1:
    babel-plugin-transform-es2015-parameters: private
  babel-plugin-transform-es2015-shorthand-properties@6.24.1:
    babel-plugin-transform-es2015-shorthand-properties: private
  babel-plugin-transform-es2015-spread@6.22.0:
    babel-plugin-transform-es2015-spread: private
  babel-plugin-transform-es2015-sticky-regex@6.24.1:
    babel-plugin-transform-es2015-sticky-regex: private
  babel-plugin-transform-es2015-template-literals@6.22.0:
    babel-plugin-transform-es2015-template-literals: private
  babel-plugin-transform-es2015-typeof-symbol@6.23.0:
    babel-plugin-transform-es2015-typeof-symbol: private
  babel-plugin-transform-es2015-unicode-regex@6.24.1:
    babel-plugin-transform-es2015-unicode-regex: private
  babel-plugin-transform-exponentiation-operator@6.24.1:
    babel-plugin-transform-exponentiation-operator: private
  babel-plugin-transform-object-rest-spread@6.26.0:
    babel-plugin-transform-object-rest-spread: private
  babel-plugin-transform-regenerator@6.26.0:
    babel-plugin-transform-regenerator: private
  babel-plugin-transform-runtime@6.23.0:
    babel-plugin-transform-runtime: private
  babel-plugin-transform-strict-mode@6.24.1:
    babel-plugin-transform-strict-mode: private
  babel-preset-env@1.7.0:
    babel-preset-env: private
  babel-register@6.26.0:
    babel-register: private
  babel-runtime@6.26.0:
    babel-runtime: private
  babel-template@6.26.0:
    babel-template: private
  babel-traverse@6.26.0:
    babel-traverse: private
  babel-types@6.26.0:
    babel-types: private
  babylon@6.18.0:
    babylon: private
  backo2@1.0.2:
    backo2: private
  balanced-match@1.0.2:
    balanced-match: private
  base64-arraybuffer@0.1.4:
    base64-arraybuffer: private
  base64-js@1.5.1:
    base64-js: private
  base64id@2.0.0:
    base64id: private
  base@0.11.2:
    base: private
  batch@0.6.1:
    batch: private
  bcrypt-pbkdf@1.0.2:
    bcrypt-pbkdf: private
  beeper@1.1.1:
    beeper: private
  big.js@5.2.2:
    big.js: private
  bin-build@2.2.0:
    bin-build: private
  bin-check@2.0.0:
    bin-check: private
  bin-version-check@2.1.0:
    bin-version-check: private
  bin-version@1.0.4:
    bin-version: private
  bin-wrapper@3.0.2:
    bin-wrapper: private
  binary-extensions@1.13.1:
    binary-extensions: private
  bl@1.2.3:
    bl: private
  blob@0.0.5:
    blob: private
  block-stream@0.0.9:
    block-stream: private
  bluebird@3.7.2:
    bluebird: private
  bn.js@5.2.2:
    bn.js: private
  body-parser@1.20.3(supports-color@5.5.0):
    body-parser: private
  bonjour@3.5.0:
    bonjour: private
  boolbase@1.0.0:
    boolbase: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@2.3.2(supports-color@5.5.0):
    braces: private
  brorand@1.1.0:
    brorand: private
  browserify-aes@1.2.0:
    browserify-aes: private
  browserify-cipher@1.0.1:
    browserify-cipher: private
  browserify-des@1.0.2:
    browserify-des: private
  browserify-rsa@4.1.1:
    browserify-rsa: private
  browserify-sign@4.2.3:
    browserify-sign: private
  browserify-zlib@0.2.0:
    browserify-zlib: private
  browserslist@2.11.3:
    browserslist: private
  buffer-alloc-unsafe@1.1.0:
    buffer-alloc-unsafe: private
  buffer-alloc@1.2.0:
    buffer-alloc: private
  buffer-crc32@0.2.13:
    buffer-crc32: private
  buffer-fill@1.0.0:
    buffer-fill: private
  buffer-from@0.1.2:
    buffer-from: private
  buffer-indexof@1.1.1:
    buffer-indexof: private
  buffer-to-vinyl@1.1.0:
    buffer-to-vinyl: private
  buffer-xor@1.0.3:
    buffer-xor: private
  buffer@4.9.2:
    buffer: private
  builtin-status-codes@3.0.0:
    builtin-status-codes: private
  bytes@3.1.2:
    bytes: private
  cacache@10.0.4:
    cacache: private
  cache-base@1.0.1:
    cache-base: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  caller-callsite@2.0.0:
    caller-callsite: private
  caller-path@2.0.0:
    caller-path: private
  callsites@2.0.0:
    callsites: private
  camel-case@3.0.0:
    camel-case: private
  camelcase-keys@2.1.0:
    camelcase-keys: private
  camelcase@4.1.0:
    camelcase: private
  caniuse-api@1.6.1:
    caniuse-api: private
  caniuse-db@1.0.30001741:
    caniuse-db: private
  caniuse-lite@1.0.30001740:
    caniuse-lite: private
  capture-stack-trace@1.0.2:
    capture-stack-trace: private
  caseless@0.12.0:
    caseless: private
  caw@1.2.0:
    caw: private
  center-align@0.1.3:
    center-align: private
  chalk@1.1.3:
    chalk: private
  charenc@0.0.2:
    charenc: private
  chokidar@1.7.0:
    chokidar: private
  chownr@1.1.4:
    chownr: private
  cipher-base@1.0.6:
    cipher-base: private
  clap@1.2.3:
    clap: private
  class-utils@0.3.6:
    class-utils: private
  clean-css@4.2.4:
    clean-css: private
  cliui@3.2.0:
    cliui: private
  clone-deep@2.0.2:
    clone-deep: private
  clone-stats@0.0.1:
    clone-stats: private
  clone@1.0.4:
    clone: private
  cluster-key-slot@1.1.2:
    cluster-key-slot: private
  co@4.6.0:
    co: private
  coa@2.0.2:
    coa: private
  code-point-at@1.1.0:
    code-point-at: private
  collection-visit@1.0.0:
    collection-visit: private
  color-convert@1.9.3:
    color-convert: private
  color-name@1.1.3:
    color-name: private
  color-string@0.3.0:
    color-string: private
  color-support@1.1.3:
    color-support: private
  color@0.11.4:
    color: private
  colormin@1.1.2:
    colormin: private
  colors@1.1.2:
    colors: private
  combined-stream@1.0.8:
    combined-stream: private
  commander@2.17.1:
    commander: private
  commondir@1.0.1:
    commondir: private
  component-bind@1.0.0:
    component-bind: private
  component-emitter@1.3.1:
    component-emitter: private
  component-inherit@0.0.3:
    component-inherit: private
  compressible@2.0.18:
    compressible: private
  compression@1.8.1(supports-color@5.5.0):
    compression: private
  concat-map@0.0.1:
    concat-map: private
  concat-stream@1.6.2:
    concat-stream: private
  concatenate@0.0.2:
    concatenate: private
  config-chain@1.1.13:
    config-chain: private
  connect-history-api-fallback@1.6.0:
    connect-history-api-fallback: private
  console-browserify@1.2.0:
    console-browserify: private
  console-control-strings@1.1.0:
    console-control-strings: private
  console-stream@0.1.1:
    console-stream: private
  consolidate@0.14.5(babel-core@6.26.3)(lodash@4.17.21):
    consolidate: private
  constants-browserify@1.0.0:
    constants-browserify: private
  content-disposition@0.5.4:
    content-disposition: private
  content-type@1.0.5:
    content-type: private
  convert-source-map@1.9.0:
    convert-source-map: private
  cookie-signature@1.0.6:
    cookie-signature: private
  cookie@0.4.2:
    cookie: private
  copy-concurrently@1.0.5:
    copy-concurrently: private
  copy-descriptor@0.1.1:
    copy-descriptor: private
  core-js@2.6.12:
    core-js: private
  core-util-is@1.0.3:
    core-util-is: private
  cosmiconfig@2.2.2:
    cosmiconfig: private
  create-ecdh@4.0.4:
    create-ecdh: private
  create-error-class@3.0.2:
    create-error-class: private
  create-hash@1.2.0:
    create-hash: private
  create-hmac@1.1.7:
    create-hmac: private
  cross-spawn@6.0.6:
    cross-spawn: private
  crypt@0.0.2:
    crypt: private
  crypto-browserify@3.12.1:
    crypto-browserify: private
  css-color-names@0.0.4:
    css-color-names: private
  css-loader@0.28.11:
    css-loader: private
  css-select-base-adapter@0.1.1:
    css-select-base-adapter: private
  css-select@2.1.0:
    css-select: private
  css-selector-tokenizer@0.7.3:
    css-selector-tokenizer: private
  css-tree@1.0.0-alpha.37:
    css-tree: private
  css-what@3.4.2:
    css-what: private
  css@2.2.4:
    css: private
  cssesc@3.0.0:
    cssesc: private
  cssnano@3.10.0:
    cssnano: private
  csso@4.2.0:
    csso: private
  csstype@3.1.3:
    csstype: private
  currently-unhandled@0.4.1:
    currently-unhandled: private
  cyclist@1.0.2:
    cyclist: private
  d@1.0.2:
    d: private
  dashdash@1.14.1:
    dashdash: private
  data-view-buffer@1.0.2:
    data-view-buffer: private
  data-view-byte-length@1.0.2:
    data-view-byte-length: private
  data-view-byte-offset@1.0.1:
    data-view-byte-offset: private
  dateformat@2.2.0:
    dateformat: private
  de-indent@1.0.2:
    de-indent: private
  debug@2.6.9(supports-color@5.5.0):
    debug: private
  decamelize@1.2.0:
    decamelize: private
  decode-uri-component@0.2.2:
    decode-uri-component: private
  decompress-response@3.3.0:
    decompress-response: private
  decompress-tar@3.1.0:
    decompress-tar: private
  decompress-tarbz2@3.1.0:
    decompress-tarbz2: private
  decompress-targz@3.1.0:
    decompress-targz: private
  decompress-unzip@3.4.0:
    decompress-unzip: private
  decompress@3.0.0:
    decompress: private
  deep-equal@1.1.2:
    deep-equal: private
  deep-extend@0.6.0:
    deep-extend: private
  define-data-property@1.1.4:
    define-data-property: private
  define-properties@1.2.1:
    define-properties: private
  define-property@2.0.2:
    define-property: private
  defined@1.0.1:
    defined: private
  del@3.0.0:
    del: private
  delayed-stream@1.0.0:
    delayed-stream: private
  delegates@1.0.0:
    delegates: private
  denque@1.5.1:
    denque: private
  depd@2.0.0:
    depd: private
  des.js@1.1.0:
    des.js: private
  destroy@1.2.0:
    destroy: private
  detect-indent@4.0.0:
    detect-indent: private
  detect-node@2.1.0:
    detect-node: private
  diffie-hellman@5.0.3:
    diffie-hellman: private
  dns-equal@1.0.0:
    dns-equal: private
  dns-packet@1.3.4:
    dns-packet: private
  dns-txt@2.0.2:
    dns-txt: private
  dom-serializer@0.2.2:
    dom-serializer: private
  domain-browser@1.2.0:
    domain-browser: private
  domelementtype@1.3.1:
    domelementtype: private
  domutils@1.7.0:
    domutils: private
  dotenv-expand@4.2.0:
    dotenv-expand: private
  dotenv@4.0.0:
    dotenv: private
  download@4.4.3:
    download: private
  dunder-proto@1.0.1:
    dunder-proto: private
  duplexer2@0.1.4:
    duplexer2: private
  duplexer3@0.1.5:
    duplexer3: private
  duplexify@3.7.1:
    duplexify: private
  each-async@1.1.1:
    each-async: private
  ecc-jsbn@0.1.2:
    ecc-jsbn: private
  ee-first@1.1.1:
    ee-first: private
  electron-to-chromium@1.5.214:
    electron-to-chromium: private
  elliptic@6.6.1:
    elliptic: private
  emoji-regex@7.0.3:
    emoji-regex: private
  emojis-list@3.0.0:
    emojis-list: private
  encodeurl@2.0.0:
    encodeurl: private
  end-of-stream@1.4.5:
    end-of-stream: private
  engine.io-client@3.5.4:
    engine.io-client: private
  engine.io-parser@2.2.1:
    engine.io-parser: private
  engine.io@3.6.2:
    engine.io: private
  enhanced-resolve@3.4.1:
    enhanced-resolve: private
  entities@2.2.0:
    entities: private
  errno@0.1.8:
    errno: private
  error-ex@1.3.2:
    error-ex: private
  error-stack-parser@2.1.4:
    error-stack-parser: private
  es-abstract@1.24.0:
    es-abstract: private
  es-array-method-boxes-properly@1.0.0:
    es-array-method-boxes-properly: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es-to-primitive@1.3.0:
    es-to-primitive: private
  es5-ext@0.10.64:
    es5-ext: private
  es6-iterator@2.0.3:
    es6-iterator: private
  es6-map@0.1.5:
    es6-map: private
  es6-set@0.1.6:
    es6-set: private
  es6-symbol@3.1.4:
    es6-symbol: private
  es6-templates@0.2.3:
    es6-templates: private
  es6-weak-map@2.0.3:
    es6-weak-map: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@1.0.5:
    escape-string-regexp: private
  escope@3.6.0:
    escope: private
  esniff@2.0.1:
    esniff: private
  esprima@3.1.3:
    esprima: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@4.3.0:
    estraverse: private
  esutils@2.0.3:
    esutils: private
  etag@1.8.1:
    etag: private
  event-emitter@0.3.5:
    event-emitter: private
  eventemitter3@4.0.7:
    eventemitter3: private
  events@3.3.0:
    events: private
  eventsource@0.1.6:
    eventsource: private
  evp_bytestokey@1.0.3:
    evp_bytestokey: private
  exec-buffer@3.2.0:
    exec-buffer: private
  exec-series@1.0.3:
    exec-series: private
  execa@0.8.0:
    execa: private
  executable@1.1.0:
    executable: private
  expand-brackets@0.1.5:
    expand-brackets: private
  expand-range@1.8.2:
    expand-range: private
  express@4.21.2(supports-color@5.5.0):
    express: private
  ext-list@2.2.2:
    ext-list: private
  ext-name@5.0.0:
    ext-name: private
  ext@1.7.0:
    ext: private
  extend-shallow@2.0.1:
    extend-shallow: private
  extend@3.0.2:
    extend: private
  extglob@0.3.2:
    extglob: private
  extract-text-webpack-plugin@3.0.2(webpack@3.12.0):
    extract-text-webpack-plugin: private
  extsprintf@1.3.0:
    extsprintf: private
  fancy-log@1.3.3:
    fancy-log: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fastparse@1.1.2:
    fastparse: private
  faye-websocket@0.11.4:
    faye-websocket: private
  fd-slicer@1.1.0:
    fd-slicer: private
  figures@1.7.0:
    figures: private
  file-loader@0.11.2:
    file-loader: private
  file-type@4.4.0:
    file-type: private
  filename-regex@2.0.1:
    filename-regex: private
  filename-reserved-regex@1.0.0:
    filename-reserved-regex: private
  filenamify@1.2.1:
    filenamify: private
  fill-range@4.0.0:
    fill-range: private
  finalhandler@1.3.1(supports-color@5.5.0):
    finalhandler: private
  find-cache-dir@1.0.0:
    find-cache-dir: private
  find-up@2.1.0:
    find-up: private
  find-versions@1.2.1:
    find-versions: private
  first-chunk-stream@1.0.0:
    first-chunk-stream: private
  flatten@1.0.3:
    flatten: private
  flexbuffer@0.0.6:
    flexbuffer: private
  flush-write-stream@1.1.1:
    flush-write-stream: private
  follow-redirects@1.15.11(debug@3.2.7(supports-color@5.5.0)):
    follow-redirects: private
  for-each@0.3.5:
    for-each: private
  for-in@1.0.2:
    for-in: private
  for-own@1.0.0:
    for-own: private
  forever-agent@0.6.1:
    forever-agent: private
  form-data@2.3.3:
    form-data: private
  forwarded@0.2.0:
    forwarded: private
  fragment-cache@0.2.1:
    fragment-cache: private
  fresh@0.5.2:
    fresh: private
  friendly-errors-webpack-plugin@1.7.0(webpack@3.12.0):
    friendly-errors-webpack-plugin: private
  from2@2.3.0:
    from2: private
  fs-constants@1.0.0:
    fs-constants: private
  fs-extra@3.0.1:
    fs-extra: private
  fs-write-stream-atomic@1.0.10:
    fs-write-stream-atomic: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@1.2.13:
    fsevents: private
  fstream@1.0.12:
    fstream: private
  function-bind@1.1.2:
    function-bind: private
  function.prototype.name@1.1.8:
    function.prototype.name: private
  functions-have-names@1.2.3:
    functions-have-names: private
  gauge@2.7.4:
    gauge: private
  gaze@1.1.3:
    gaze: private
  get-caller-file@1.0.3:
    get-caller-file: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  get-proxy@1.1.0:
    get-proxy: private
  get-stdin@4.0.1:
    get-stdin: private
  get-stream@3.0.0:
    get-stream: private
  get-symbol-description@1.1.0:
    get-symbol-description: private
  get-value@2.0.6:
    get-value: private
  getpass@0.1.7:
    getpass: private
  gifsicle@3.0.4:
    gifsicle: private
  glob-base@0.3.0:
    glob-base: private
  glob-parent@2.0.0:
    glob-parent: private
  glob-stream@5.3.5:
    glob-stream: private
  glob@7.2.3:
    glob: private
  globals@9.18.0:
    globals: private
  globalthis@1.0.4:
    globalthis: private
  globby@6.1.0:
    globby: private
  globs@0.1.4:
    globs: private
  globule@1.3.4:
    globule: private
  glogg@1.0.2:
    glogg: private
  gopd@1.2.0:
    gopd: private
  got@5.7.1:
    got: private
  graceful-fs@4.2.11:
    graceful-fs: private
  growly@1.3.0:
    growly: private
  gulp-decompress@1.2.0:
    gulp-decompress: private
  gulp-rename@1.4.0:
    gulp-rename: private
  gulp-sourcemaps@1.6.0:
    gulp-sourcemaps: private
  gulp-util@3.0.8:
    gulp-util: private
  gulplog@1.0.0:
    gulplog: private
  handle-thing@2.0.1:
    handle-thing: private
  har-schema@2.0.0:
    har-schema: private
  har-validator@5.1.5:
    har-validator: private
  has-ansi@2.0.0:
    has-ansi: private
  has-bigints@1.1.0:
    has-bigints: private
  has-binary2@1.0.3:
    has-binary2: private
  has-cors@1.1.0:
    has-cors: private
  has-flag@2.0.0:
    has-flag: private
  has-gulplog@0.1.0:
    has-gulplog: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.2.0:
    has-proto: private
  has-symbol-support-x@1.4.2:
    has-symbol-support-x: private
  has-symbols@1.1.0:
    has-symbols: private
  has-to-string-tag-x@1.4.1:
    has-to-string-tag-x: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  has-unicode@2.0.1:
    has-unicode: private
  has-value@1.0.0:
    has-value: private
  has-values@1.0.0:
    has-values: private
  has@1.0.4:
    has: private
  hash-base@3.0.5:
    hash-base: private
  hash-sum@1.0.2:
    hash-sum: private
  hash.js@1.1.7:
    hash.js: private
  hasown@2.0.2:
    hasown: private
  he@1.2.0:
    he: private
  hmac-drbg@1.0.1:
    hmac-drbg: private
  home-or-tmp@2.0.0:
    home-or-tmp: private
  hosted-git-info@2.8.9:
    hosted-git-info: private
  hpack.js@2.1.6:
    hpack.js: private
  html-comment-regex@1.1.2:
    html-comment-regex: private
  html-entities@1.4.0:
    html-entities: private
  html-loader@0.4.5:
    html-loader: private
  html-minifier@3.5.21:
    html-minifier: private
  http-deceiver@1.2.7:
    http-deceiver: private
  http-errors@2.0.0:
    http-errors: private
  http-parser-js@0.5.10:
    http-parser-js: private
  http-proxy-middleware@0.19.2(debug@3.2.7(supports-color@5.5.0))(supports-color@5.5.0):
    http-proxy-middleware: private
  http-proxy@1.18.1(debug@3.2.7(supports-color@5.5.0)):
    http-proxy: private
  http-signature@1.2.0:
    http-signature: private
  https-browserify@1.0.0:
    https-browserify: private
  iconv-lite@0.4.24:
    iconv-lite: private
  icss-replace-symbols@1.1.0:
    icss-replace-symbols: private
  icss-utils@2.1.0:
    icss-utils: private
  ieee754@1.2.1:
    ieee754: private
  iferr@0.1.5:
    iferr: private
  imagemin-gifsicle@5.2.0:
    imagemin-gifsicle: private
  imagemin-mozjpeg@7.0.0:
    imagemin-mozjpeg: private
  imagemin-optipng@5.2.1:
    imagemin-optipng: private
  imagemin-pngquant@5.1.0:
    imagemin-pngquant: private
  imagemin-svgo@6.0.0:
    imagemin-svgo: private
  imagemin@5.3.1:
    imagemin: private
  img-loader@2.0.1:
    img-loader: private
  import-cwd@2.1.0:
    import-cwd: private
  import-fresh@2.0.0:
    import-fresh: private
  import-from@2.1.0:
    import-from: private
  import-local@1.0.0:
    import-local: private
  imurmurhash@0.1.4:
    imurmurhash: private
  in-publish@2.0.1:
    in-publish: private
  indent-string@2.1.0:
    indent-string: private
  indexes-of@1.0.1:
    indexes-of: private
  indexof@0.0.1:
    indexof: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  ini@1.3.8:
    ini: private
  internal-ip@1.2.0:
    internal-ip: private
  internal-slot@1.1.0:
    internal-slot: private
  interpret@1.4.0:
    interpret: private
  invariant@2.2.4:
    invariant: private
  invert-kv@1.0.0:
    invert-kv: private
  ip-regex@1.0.3:
    ip-regex: private
  ip@1.1.9:
    ip: private
  ipaddr.js@1.9.1:
    ipaddr.js: private
  is-absolute-url@2.1.0:
    is-absolute-url: private
  is-absolute@0.1.7:
    is-absolute: private
  is-accessor-descriptor@1.0.1:
    is-accessor-descriptor: private
  is-arguments@1.2.0:
    is-arguments: private
  is-array-buffer@3.0.5:
    is-array-buffer: private
  is-arrayish@0.2.1:
    is-arrayish: private
  is-async-function@2.1.1:
    is-async-function: private
  is-bigint@1.1.0:
    is-bigint: private
  is-binary-path@1.0.1:
    is-binary-path: private
  is-boolean-object@1.2.2:
    is-boolean-object: private
  is-buffer@1.1.6:
    is-buffer: private
  is-bzip2@1.0.0:
    is-bzip2: private
  is-callable@1.2.7:
    is-callable: private
  is-core-module@2.16.1:
    is-core-module: private
  is-data-descriptor@1.0.1:
    is-data-descriptor: private
  is-data-view@1.0.2:
    is-data-view: private
  is-date-object@1.1.0:
    is-date-object: private
  is-descriptor@1.0.3:
    is-descriptor: private
  is-directory@0.3.1:
    is-directory: private
  is-docker@2.2.1:
    is-docker: private
  is-dotfile@1.0.3:
    is-dotfile: private
  is-equal-shallow@0.1.3:
    is-equal-shallow: private
  is-extendable@0.1.1:
    is-extendable: private
  is-extglob@1.0.0:
    is-extglob: private
  is-finalizationregistry@1.1.1:
    is-finalizationregistry: private
  is-finite@1.1.0:
    is-finite: private
  is-fullwidth-code-point@2.0.0:
    is-fullwidth-code-point: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-gif@1.0.0:
    is-gif: private
  is-glob@2.0.1:
    is-glob: private
  is-gzip@1.0.0:
    is-gzip: private
  is-jpg@1.0.1:
    is-jpg: private
  is-map@2.0.3:
    is-map: private
  is-natural-number@4.0.1:
    is-natural-number: private
  is-negative-zero@2.0.3:
    is-negative-zero: private
  is-number-object@1.1.1:
    is-number-object: private
  is-number@3.0.0:
    is-number: private
  is-obj@1.0.1:
    is-obj: private
  is-object@1.0.2:
    is-object: private
  is-path-cwd@1.0.0:
    is-path-cwd: private
  is-path-in-cwd@1.0.1:
    is-path-in-cwd: private
  is-path-inside@1.0.1:
    is-path-inside: private
  is-plain-obj@1.1.0:
    is-plain-obj: private
  is-plain-object@2.0.4:
    is-plain-object: private
  is-png@1.1.0:
    is-png: private
  is-posix-bracket@0.1.1:
    is-posix-bracket: private
  is-primitive@2.0.0:
    is-primitive: private
  is-redirect@1.0.0:
    is-redirect: private
  is-regex@1.2.1:
    is-regex: private
  is-relative@0.1.3:
    is-relative: private
  is-retry-allowed@1.2.0:
    is-retry-allowed: private
  is-set@2.0.3:
    is-set: private
  is-shared-array-buffer@1.0.4:
    is-shared-array-buffer: private
  is-stream@1.1.0:
    is-stream: private
  is-string@1.1.1:
    is-string: private
  is-svg@2.1.0:
    is-svg: private
  is-symbol@1.1.1:
    is-symbol: private
  is-tar@1.0.0:
    is-tar: private
  is-typed-array@1.1.15:
    is-typed-array: private
  is-typedarray@1.0.0:
    is-typedarray: private
  is-url@1.2.4:
    is-url: private
  is-utf8@0.2.1:
    is-utf8: private
  is-valid-glob@0.3.0:
    is-valid-glob: private
  is-weakmap@2.0.2:
    is-weakmap: private
  is-weakref@1.1.1:
    is-weakref: private
  is-weakset@2.0.4:
    is-weakset: private
  is-windows@1.0.2:
    is-windows: private
  is-wsl@2.2.0:
    is-wsl: private
  is-zip@1.0.0:
    is-zip: private
  isarray@2.0.1:
    isarray: private
  isexe@2.0.0:
    isexe: private
  isobject@3.0.1:
    isobject: private
  isstream@0.1.2:
    isstream: private
  isurl@1.0.0:
    isurl: private
  js-base64@2.6.4:
    js-base64: private
  js-tokens@3.0.2:
    js-tokens: private
  js-yaml@3.14.1:
    js-yaml: private
  jsbn@0.1.1:
    jsbn: private
  jsesc@1.3.0:
    jsesc: private
  json-loader@0.5.7:
    json-loader: private
  json-parse-better-errors@1.0.2:
    json-parse-better-errors: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-schema@0.4.0:
    json-schema: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json-stringify-safe@5.0.1:
    json-stringify-safe: private
  json3@3.3.3:
    json3: private
  json5@0.5.1:
    json5: private
  jsonfile@3.0.1:
    jsonfile: private
  jsprim@1.4.2:
    jsprim: private
  killable@1.0.1:
    killable: private
  kind-of@6.0.3:
    kind-of: private
  lazy-cache@1.0.4:
    lazy-cache: private
  lazy-req@1.1.0:
    lazy-req: private
  lazystream@1.0.1:
    lazystream: private
  lcid@1.0.0:
    lcid: private
  load-json-file@2.0.0:
    load-json-file: private
  loader-runner@2.4.0:
    loader-runner: private
  loader-utils@1.4.2:
    loader-utils: private
  locate-path@2.0.0:
    locate-path: private
  lodash._baseassign@3.2.0:
    lodash._baseassign: private
  lodash._basecopy@3.0.1:
    lodash._basecopy: private
  lodash._basetostring@3.0.1:
    lodash._basetostring: private
  lodash._basevalues@3.0.0:
    lodash._basevalues: private
  lodash._bindcallback@3.0.1:
    lodash._bindcallback: private
  lodash._createassigner@3.1.1:
    lodash._createassigner: private
  lodash._getnative@3.9.1:
    lodash._getnative: private
  lodash._isiterateecall@3.0.9:
    lodash._isiterateecall: private
  lodash._reescape@3.0.0:
    lodash._reescape: private
  lodash._reevaluate@3.0.0:
    lodash._reevaluate: private
  lodash._reinterpolate@3.0.0:
    lodash._reinterpolate: private
  lodash._root@3.0.1:
    lodash._root: private
  lodash.assign@4.2.0:
    lodash.assign: private
  lodash.bind@4.2.1:
    lodash.bind: private
  lodash.camelcase@4.3.0:
    lodash.camelcase: private
  lodash.clone@4.5.0:
    lodash.clone: private
  lodash.clonedeep@4.5.0:
    lodash.clonedeep: private
  lodash.defaults@4.2.0:
    lodash.defaults: private
  lodash.difference@4.5.0:
    lodash.difference: private
  lodash.escape@3.2.0:
    lodash.escape: private
  lodash.flatten@4.4.0:
    lodash.flatten: private
  lodash.foreach@4.5.0:
    lodash.foreach: private
  lodash.isarguments@3.1.0:
    lodash.isarguments: private
  lodash.isarray@3.0.4:
    lodash.isarray: private
  lodash.isempty@4.4.0:
    lodash.isempty: private
  lodash.isequal@4.5.0:
    lodash.isequal: private
  lodash.keys@4.2.0:
    lodash.keys: private
  lodash.memoize@4.1.2:
    lodash.memoize: private
  lodash.noop@3.0.1:
    lodash.noop: private
  lodash.partial@4.2.1:
    lodash.partial: private
  lodash.pick@4.4.0:
    lodash.pick: private
  lodash.restparam@3.6.1:
    lodash.restparam: private
  lodash.sample@4.2.1:
    lodash.sample: private
  lodash.shuffle@4.2.0:
    lodash.shuffle: private
  lodash.tail@4.1.1:
    lodash.tail: private
  lodash.template@3.6.2:
    lodash.template: private
  lodash.templatesettings@3.1.1:
    lodash.templatesettings: private
  lodash.uniq@4.5.0:
    lodash.uniq: private
  lodash.values@4.3.0:
    lodash.values: private
  logalot@2.1.0:
    logalot: private
  loglevel@1.9.2:
    loglevel: private
  longest@1.0.1:
    longest: private
  loose-envify@1.4.0:
    loose-envify: private
  loud-rejection@1.6.0:
    loud-rejection: private
  lower-case@1.1.4:
    lower-case: private
  lowercase-keys@1.0.1:
    lowercase-keys: private
  lpad-align@1.1.2:
    lpad-align: private
  lru-cache@4.1.5:
    lru-cache: private
  make-dir@1.3.0:
    make-dir: private
  map-cache@0.2.2:
    map-cache: private
  map-obj@1.0.1:
    map-obj: private
  map-visit@1.0.0:
    map-visit: private
  math-expression-evaluator@1.4.0:
    math-expression-evaluator: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  math-random@1.0.4:
    math-random: private
  md5.js@1.3.5:
    md5.js: private
  md5@2.3.0:
    md5: private
  mdn-data@2.0.4:
    mdn-data: private
  media-typer@0.3.0:
    media-typer: private
  mem@1.1.0:
    mem: private
  memory-fs@0.4.1:
    memory-fs: private
  meow@3.7.0:
    meow: private
  merge-descriptors@1.0.3:
    merge-descriptors: private
  merge-stream@1.0.1:
    merge-stream: private
  methods@1.1.2:
    methods: private
  micromatch@2.3.11:
    micromatch: private
  miller-rabin@4.0.1:
    miller-rabin: private
  mime-db@1.54.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mime@1.6.0:
    mime: private
  mimic-fn@1.2.0:
    mimic-fn: private
  mimic-response@1.0.1:
    mimic-response: private
  minimalistic-assert@1.0.1:
    minimalistic-assert: private
  minimalistic-crypto-utils@1.0.1:
    minimalistic-crypto-utils: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  mississippi@2.0.0:
    mississippi: private
  mixin-deep@1.3.2:
    mixin-deep: private
  mixin-object@2.0.1:
    mixin-object: private
  mkdirp@0.5.6:
    mkdirp: private
  move-concurrently@1.0.1:
    move-concurrently: private
  mozjpeg@5.0.0:
    mozjpeg: private
  ms@2.0.0:
    ms: private
  multicast-dns-service-types@1.1.0:
    multicast-dns-service-types: private
  multicast-dns@6.2.3:
    multicast-dns: private
  multipipe@0.1.2:
    multipipe: private
  nan@2.23.0:
    nan: private
  nanoid@3.3.11:
    nanoid: private
  nanomatch@1.2.13:
    nanomatch: private
  negotiator@0.6.4:
    negotiator: private
  neo-async@2.6.2:
    neo-async: private
  next-tick@1.1.0:
    next-tick: private
  nice-try@1.0.5:
    nice-try: private
  no-case@2.3.2:
    no-case: private
  node-forge@0.10.0:
    node-forge: private
  node-gyp@3.8.0:
    node-gyp: private
  node-libs-browser@2.2.1:
    node-libs-browser: private
  node-notifier@9.0.1:
    node-notifier: private
  node-sass@4.14.1:
    node-sass: private
  node-status-codes@1.0.0:
    node-status-codes: private
  nopt@3.0.6:
    nopt: private
  normalize-package-data@2.5.0:
    normalize-package-data: private
  normalize-path@2.1.1:
    normalize-path: private
  normalize-range@0.1.2:
    normalize-range: private
  normalize-url@1.9.1:
    normalize-url: private
  npm-conf@1.1.3:
    npm-conf: private
  npm-run-path@2.0.2:
    npm-run-path: private
  npmlog@4.1.2:
    npmlog: private
  nth-check@1.0.2:
    nth-check: private
  num2fraction@1.2.2:
    num2fraction: private
  number-is-nan@1.0.1:
    number-is-nan: private
  oauth-sign@0.9.0:
    oauth-sign: private
  object-assign@4.1.1:
    object-assign: private
  object-copy@0.1.0:
    object-copy: private
  object-inspect@1.13.4:
    object-inspect: private
  object-is@1.1.6:
    object-is: private
  object-keys@1.1.1:
    object-keys: private
  object-path@0.9.2:
    object-path: private
  object-visit@1.0.1:
    object-visit: private
  object.assign@4.1.7:
    object.assign: private
  object.getownpropertydescriptors@2.1.8:
    object.getownpropertydescriptors: private
  object.omit@2.0.1:
    object.omit: private
  object.pick@1.3.0:
    object.pick: private
  object.values@1.2.1:
    object.values: private
  obuf@1.1.2:
    obuf: private
  on-finished@2.4.1:
    on-finished: private
  on-headers@1.1.0:
    on-headers: private
  once@1.4.0:
    once: private
  onetime@1.1.0:
    onetime: private
  opn@5.5.0:
    opn: private
  optipng-bin@3.1.4:
    optipng-bin: private
  ordered-read-streams@0.3.0:
    ordered-read-streams: private
  original@1.0.2:
    original: private
  os-browserify@0.3.0:
    os-browserify: private
  os-filter-obj@1.0.3:
    os-filter-obj: private
  os-homedir@1.0.2:
    os-homedir: private
  os-locale@2.1.0:
    os-locale: private
  os-tmpdir@1.0.2:
    os-tmpdir: private
  osenv@0.1.5:
    osenv: private
  own-keys@1.0.1:
    own-keys: private
  p-cancelable@0.3.0:
    p-cancelable: private
  p-event@1.3.0:
    p-event: private
  p-finally@1.0.0:
    p-finally: private
  p-limit@1.3.0:
    p-limit: private
  p-locate@2.0.0:
    p-locate: private
  p-map-series@1.0.0:
    p-map-series: private
  p-map@1.2.0:
    p-map: private
  p-pipe@1.2.0:
    p-pipe: private
  p-reduce@1.0.0:
    p-reduce: private
  p-timeout@1.2.1:
    p-timeout: private
  p-try@1.0.0:
    p-try: private
  pako@1.0.11:
    pako: private
  parallel-transform@1.2.0:
    parallel-transform: private
  param-case@2.1.1:
    param-case: private
  parse-asn1@5.1.7:
    parse-asn1: private
  parse-glob@3.0.4:
    parse-glob: private
  parse-json@2.2.0:
    parse-json: private
  parse-node-version@1.0.1:
    parse-node-version: private
  parseqs@0.0.6:
    parseqs: private
  parseuri@0.0.6:
    parseuri: private
  parseurl@1.3.3:
    parseurl: private
  pascalcase@0.1.1:
    pascalcase: private
  path-browserify@0.0.1:
    path-browserify: private
  path-dirname@1.0.2:
    path-dirname: private
  path-exists@2.1.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-is-inside@1.0.2:
    path-is-inside: private
  path-key@2.0.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-to-regexp@0.1.12:
    path-to-regexp: private
  path-type@2.0.0:
    path-type: private
  pbkdf2@3.1.3:
    pbkdf2: private
  pend@1.2.0:
    pend: private
  performance-now@2.1.0:
    performance-now: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  pify@3.0.0:
    pify: private
  pinkie-promise@2.0.1:
    pinkie-promise: private
  pinkie@2.0.4:
    pinkie: private
  pkg-dir@2.0.0:
    pkg-dir: private
  pngquant-bin@4.0.0:
    pngquant-bin: private
  portfinder@1.0.37(supports-color@5.5.0):
    portfinder: private
  posix-character-classes@0.1.1:
    posix-character-classes: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  postcss-calc@5.3.1:
    postcss-calc: private
  postcss-colormin@2.2.2:
    postcss-colormin: private
  postcss-convert-values@2.6.1:
    postcss-convert-values: private
  postcss-discard-comments@2.0.4:
    postcss-discard-comments: private
  postcss-discard-duplicates@2.1.0:
    postcss-discard-duplicates: private
  postcss-discard-empty@2.1.0:
    postcss-discard-empty: private
  postcss-discard-overridden@0.1.1:
    postcss-discard-overridden: private
  postcss-discard-unused@2.2.3:
    postcss-discard-unused: private
  postcss-filter-plugins@2.0.3:
    postcss-filter-plugins: private
  postcss-load-config@2.1.2:
    postcss-load-config: private
  postcss-load-options@1.2.0:
    postcss-load-options: private
  postcss-load-plugins@2.3.0:
    postcss-load-plugins: private
  postcss-loader@2.1.6:
    postcss-loader: private
  postcss-merge-idents@2.1.7:
    postcss-merge-idents: private
  postcss-merge-longhand@2.0.2:
    postcss-merge-longhand: private
  postcss-merge-rules@2.1.2:
    postcss-merge-rules: private
  postcss-message-helpers@2.0.0:
    postcss-message-helpers: private
  postcss-minify-font-values@1.0.5:
    postcss-minify-font-values: private
  postcss-minify-gradients@1.0.5:
    postcss-minify-gradients: private
  postcss-minify-params@1.2.2:
    postcss-minify-params: private
  postcss-minify-selectors@2.1.1:
    postcss-minify-selectors: private
  postcss-modules-extract-imports@1.2.1:
    postcss-modules-extract-imports: private
  postcss-modules-local-by-default@1.2.0:
    postcss-modules-local-by-default: private
  postcss-modules-scope@1.1.0:
    postcss-modules-scope: private
  postcss-modules-values@1.3.0:
    postcss-modules-values: private
  postcss-normalize-charset@1.1.1:
    postcss-normalize-charset: private
  postcss-normalize-url@3.0.8:
    postcss-normalize-url: private
  postcss-ordered-values@2.2.3:
    postcss-ordered-values: private
  postcss-reduce-idents@2.4.0:
    postcss-reduce-idents: private
  postcss-reduce-initial@1.0.1:
    postcss-reduce-initial: private
  postcss-reduce-transforms@1.0.4:
    postcss-reduce-transforms: private
  postcss-selector-parser@2.2.3:
    postcss-selector-parser: private
  postcss-svgo@2.1.6:
    postcss-svgo: private
  postcss-unique-selectors@2.0.2:
    postcss-unique-selectors: private
  postcss-value-parser@3.3.1:
    postcss-value-parser: private
  postcss-zindex@2.2.0:
    postcss-zindex: private
  postcss@8.5.6:
    postcss: private
  prepend-http@1.0.4:
    prepend-http: private
  preserve@0.2.0:
    preserve: private
  prettier@2.8.8:
    prettier: private
  private@0.1.8:
    private: private
  process-nextick-args@2.0.1:
    process-nextick-args: private
  process@0.11.10:
    process: private
  promise-inflight@1.0.1(bluebird@3.7.2):
    promise-inflight: private
  proto-list@1.2.4:
    proto-list: private
  proxy-addr@2.0.7:
    proxy-addr: private
  prr@1.0.1:
    prr: private
  pseudomap@1.0.2:
    pseudomap: private
  psl@1.15.0:
    psl: private
  public-encrypt@4.0.3:
    public-encrypt: private
  pump@2.0.1:
    pump: private
  pumpify@1.5.1:
    pumpify: private
  punycode@1.4.1:
    punycode: private
  q@1.5.1:
    q: private
  qs@6.13.0:
    qs: private
  query-string@4.3.4:
    query-string: private
  querystring-es3@0.2.1:
    querystring-es3: private
  querystringify@2.2.0:
    querystringify: private
  randomatic@3.1.1:
    randomatic: private
  randombytes@2.1.0:
    randombytes: private
  randomfill@1.0.4:
    randomfill: private
  range-parser@1.2.1:
    range-parser: private
  raw-body@2.5.2:
    raw-body: private
  rc@1.2.8:
    rc: private
  read-all-stream@3.1.0:
    read-all-stream: private
  read-pkg-up@2.0.0:
    read-pkg-up: private
  read-pkg@2.0.0:
    read-pkg: private
  readable-stream@2.3.8:
    readable-stream: private
  readdirp@2.2.1:
    readdirp: private
  recast@0.11.23:
    recast: private
  redent@1.0.0:
    redent: private
  redis-commands@1.7.0:
    redis-commands: private
  redis-parser@2.6.0:
    redis-parser: private
  reduce-css-calc@1.3.0:
    reduce-css-calc: private
  reduce-function-call@1.0.3:
    reduce-function-call: private
  reflect.getprototypeof@1.0.10:
    reflect.getprototypeof: private
  regenerate@1.4.2:
    regenerate: private
  regenerator-runtime@0.11.1:
    regenerator-runtime: private
  regenerator-transform@0.10.1:
    regenerator-transform: private
  regex-cache@0.4.4:
    regex-cache: private
  regex-not@1.0.2:
    regex-not: private
  regex-parser@2.3.1:
    regex-parser: private
  regexp.prototype.flags@1.5.4:
    regexp.prototype.flags: private
  regexpu-core@2.0.0:
    regexpu-core: private
  regjsgen@0.2.0:
    regjsgen: private
  regjsparser@0.1.5:
    regjsparser: private
  relateurl@0.2.7:
    relateurl: private
  remove-trailing-separator@1.1.0:
    remove-trailing-separator: private
  repeat-element@1.1.4:
    repeat-element: private
  repeat-string@1.6.1:
    repeat-string: private
  repeating@2.0.1:
    repeating: private
  replace-ext@1.0.1:
    replace-ext: private
  request@2.88.2:
    request: private
  require-directory@2.1.1:
    require-directory: private
  require-from-string@1.2.1:
    require-from-string: private
  require-main-filename@1.0.1:
    require-main-filename: private
  requires-port@1.0.0:
    requires-port: private
  resolve-cwd@2.0.0:
    resolve-cwd: private
  resolve-from@3.0.0:
    resolve-from: private
  resolve-url-loader@2.3.2:
    resolve-url-loader: private
  resolve-url@0.2.1:
    resolve-url: private
  resolve@1.22.10:
    resolve: private
  ret@0.1.15:
    ret: private
  rework-visit@1.0.0:
    rework-visit: private
  rework@1.0.1:
    rework: private
  right-align@0.1.3:
    right-align: private
  rimraf@2.7.1:
    rimraf: private
  ripemd160@2.0.2:
    ripemd160: private
  run-queue@1.0.3:
    run-queue: private
  safe-array-concat@1.1.3:
    safe-array-concat: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-push-apply@1.0.0:
    safe-push-apply: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  safe-regex@1.1.0:
    safe-regex: private
  safer-buffer@2.1.2:
    safer-buffer: private
  sass-graph@2.2.5:
    sass-graph: private
  sass-loader@6.0.7(node-sass@4.14.1)(webpack@3.12.0):
    sass-loader: private
  sax@1.2.4:
    sax: private
  schema-utils@0.3.0:
    schema-utils: private
  scss-tokenizer@0.2.3:
    scss-tokenizer: private
  seek-bzip@1.0.6:
    seek-bzip: private
  select-hose@2.0.0:
    select-hose: private
  selfsigned@1.10.14:
    selfsigned: private
  semver-regex@1.0.0:
    semver-regex: private
  semver-truncate@1.1.2:
    semver-truncate: private
  semver@5.7.2:
    semver: private
  send@0.19.0(supports-color@5.5.0):
    send: private
  serialize-javascript@1.9.1:
    serialize-javascript: private
  serve-index@1.9.1(supports-color@5.5.0):
    serve-index: private
  serve-static@1.16.2(supports-color@5.5.0):
    serve-static: private
  set-blocking@2.0.0:
    set-blocking: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  set-immediate-shim@1.0.1:
    set-immediate-shim: private
  set-proto@1.0.0:
    set-proto: private
  set-value@2.0.1:
    set-value: private
  setimmediate@1.0.5:
    setimmediate: private
  setprototypeof@1.2.0:
    setprototypeof: private
  sha.js@2.4.12:
    sha.js: private
  shallow-clone@1.0.0:
    shallow-clone: private
  shebang-command@1.2.0:
    shebang-command: private
  shebang-regex@1.0.0:
    shebang-regex: private
  shellwords@0.1.1:
    shellwords: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  signal-exit@3.0.7:
    signal-exit: private
  slash@1.0.0:
    slash: private
  snapdragon-node@2.1.1:
    snapdragon-node: private
  snapdragon-util@3.0.1:
    snapdragon-util: private
  snapdragon@0.8.2(supports-color@5.5.0):
    snapdragon: private
  socket.io-adapter@1.1.2:
    socket.io-adapter: private
  socket.io-client@2.5.0:
    socket.io-client: private
  socket.io-parser@3.4.3:
    socket.io-parser: private
  sockjs-client@1.1.5(supports-color@5.5.0):
    sockjs-client: private
  sockjs@0.3.19:
    sockjs: private
  sort-keys-length@1.0.1:
    sort-keys-length: private
  sort-keys@1.1.2:
    sort-keys: private
  source-list-map@2.0.1:
    source-list-map: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-resolve@0.5.3:
    source-map-resolve: private
  source-map-support@0.4.18:
    source-map-support: private
  source-map-url@0.4.1:
    source-map-url: private
  source-map@0.6.1:
    source-map: private
  sparkles@1.0.1:
    sparkles: private
  spdx-correct@3.2.0:
    spdx-correct: private
  spdx-exceptions@2.5.0:
    spdx-exceptions: private
  spdx-expression-parse@3.0.1:
    spdx-expression-parse: private
  spdx-license-ids@3.0.22:
    spdx-license-ids: private
  spdy-transport@3.0.0(supports-color@5.5.0):
    spdy-transport: private
  spdy@4.0.2(supports-color@5.5.0):
    spdy: private
  split-string@3.1.0:
    split-string: private
  sprintf-js@1.0.3:
    sprintf-js: private
  squeak@1.3.0:
    squeak: private
  sshpk@1.18.0:
    sshpk: private
  ssri@5.3.0:
    ssri: private
  stable@0.1.8:
    stable: private
  stackframe@1.3.4:
    stackframe: private
  stat-mode@0.2.2:
    stat-mode: private
  static-extend@0.1.2:
    static-extend: private
  statuses@2.0.1:
    statuses: private
  stdout-stream@1.4.1:
    stdout-stream: private
  stop-iteration-iterator@1.1.0:
    stop-iteration-iterator: private
  stream-browserify@2.0.2:
    stream-browserify: private
  stream-combiner2@1.1.1:
    stream-combiner2: private
  stream-each@1.2.3:
    stream-each: private
  stream-http@2.8.3:
    stream-http: private
  stream-shift@1.0.3:
    stream-shift: private
  strict-uri-encode@1.1.0:
    strict-uri-encode: private
  string-width@2.1.1:
    string-width: private
  string.prototype.trim@1.2.10:
    string.prototype.trim: private
  string.prototype.trimend@1.0.9:
    string.prototype.trimend: private
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: private
  string_decoder@1.3.0:
    string_decoder: private
  strip-ansi@3.0.1:
    strip-ansi: private
  strip-bom-stream@1.0.0:
    strip-bom-stream: private
  strip-bom@3.0.0:
    strip-bom: private
  strip-dirs@2.1.0:
    strip-dirs: private
  strip-eof@1.0.0:
    strip-eof: private
  strip-indent@1.0.1:
    strip-indent: private
  strip-json-comments@2.0.1:
    strip-json-comments: private
  strip-outer@1.0.1:
    strip-outer: private
  style-loader@0.18.2:
    style-loader: private
  sum-up@1.0.3:
    sum-up: private
  supports-color@5.5.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  svgo@1.3.2:
    svgo: private
  tapable@0.2.9:
    tapable: private
  tar-stream@1.6.2:
    tar-stream: private
  tar@2.2.2:
    tar: private
  temp-dir@1.0.0:
    temp-dir: private
  tempfile@2.0.0:
    tempfile: private
  through2-filter@2.0.0:
    through2-filter: private
  through2@2.0.5:
    through2: private
  through@2.3.8:
    through: private
  thunky@1.1.0:
    thunky: private
  time-stamp@2.2.0:
    time-stamp: private
  timed-out@3.1.3:
    timed-out: private
  timers-browserify@2.0.12:
    timers-browserify: private
  to-absolute-glob@0.1.1:
    to-absolute-glob: private
  to-array@0.1.4:
    to-array: private
  to-arraybuffer@1.0.1:
    to-arraybuffer: private
  to-buffer@1.2.1:
    to-buffer: private
  to-fast-properties@1.0.3:
    to-fast-properties: private
  to-object-path@0.3.0:
    to-object-path: private
  to-regex-range@2.1.1:
    to-regex-range: private
  to-regex@3.0.2:
    to-regex: private
  toidentifier@1.0.1:
    toidentifier: private
  tough-cookie@2.5.0:
    tough-cookie: private
  trim-newlines@1.0.0:
    trim-newlines: private
  trim-repeated@1.0.0:
    trim-repeated: private
  trim-right@1.0.1:
    trim-right: private
  true-case-path@1.0.3:
    true-case-path: private
  tty-browserify@0.0.0:
    tty-browserify: private
  tunnel-agent@0.6.0:
    tunnel-agent: private
  tweetnacl@0.14.5:
    tweetnacl: private
  type-is@1.6.18:
    type-is: private
  type@2.7.3:
    type: private
  typed-array-buffer@1.0.3:
    typed-array-buffer: private
  typed-array-byte-length@1.0.3:
    typed-array-byte-length: private
  typed-array-byte-offset@1.0.4:
    typed-array-byte-offset: private
  typed-array-length@1.0.7:
    typed-array-length: private
  typedarray@0.0.6:
    typedarray: private
  uglify-es@3.3.9:
    uglify-es: private
  uglify-js@2.8.29:
    uglify-js: private
  uglify-to-browserify@1.0.2:
    uglify-to-browserify: private
  uglifyjs-webpack-plugin@1.3.0(webpack@3.12.0):
    uglifyjs-webpack-plugin: private
  unbox-primitive@1.1.0:
    unbox-primitive: private
  unbzip2-stream@1.4.3:
    unbzip2-stream: private
  undici-types@7.10.0:
    undici-types: private
  union-value@1.0.1:
    union-value: private
  uniq@1.0.1:
    uniq: private
  uniqs@2.0.0:
    uniqs: private
  unique-filename@1.1.1:
    unique-filename: private
  unique-slug@2.0.2:
    unique-slug: private
  unique-stream@2.3.1:
    unique-stream: private
  universalify@0.1.2:
    universalify: private
  unpipe@1.0.0:
    unpipe: private
  unquote@1.1.1:
    unquote: private
  unset-value@1.0.0:
    unset-value: private
  unzip-response@1.0.2:
    unzip-response: private
  upath@1.2.0:
    upath: private
  upper-case@1.1.3:
    upper-case: private
  uri-js@4.4.1:
    uri-js: private
  urix@0.1.0:
    urix: private
  url-parse-lax@1.0.0:
    url-parse-lax: private
  url-parse@1.5.10:
    url-parse: private
  url-regex@3.2.0:
    url-regex: private
  url-to-options@1.0.1:
    url-to-options: private
  url@0.11.4:
    url: private
  use@3.1.1:
    use: private
  util-deprecate@1.0.2:
    util-deprecate: private
  util.promisify@1.0.1:
    util.promisify: private
  util@0.11.1:
    util: private
  utils-merge@1.0.1:
    utils-merge: private
  uuid@8.3.2:
    uuid: private
  vali-date@1.0.0:
    vali-date: private
  validate-npm-package-license@3.0.4:
    validate-npm-package-license: private
  vary@1.1.2:
    vary: private
  vendors@1.0.4:
    vendors: private
  verror@1.10.0:
    verror: private
  vinyl-assign@1.2.1:
    vinyl-assign: private
  vinyl-fs@2.4.4:
    vinyl-fs: private
  vinyl@1.2.0:
    vinyl: private
  vm-browserify@1.1.2:
    vm-browserify: private
  vue-hot-reload-api@2.3.4:
    vue-hot-reload-api: private
  vue-loader@13.7.3(babel-core@6.26.3)(css-loader@0.28.11)(lodash@4.17.21)(vue-template-compiler@2.7.16)(webpack@3.12.0):
    vue-loader: private
  vue-style-loader@3.1.2:
    vue-style-loader: private
  vue-template-compiler@2.7.16:
    vue-template-compiler: private
  vue-template-es2015-compiler@1.9.1:
    vue-template-es2015-compiler: private
  ware@1.3.0:
    ware: private
  watchpack-chokidar2@2.0.1(supports-color@4.5.0):
    watchpack-chokidar2: private
  watchpack@1.7.5(supports-color@4.5.0):
    watchpack: private
  wbuf@1.7.3:
    wbuf: private
  webpack-chunk-hash@0.4.0:
    webpack-chunk-hash: private
  webpack-dev-middleware@1.12.2(webpack@3.12.0):
    webpack-dev-middleware: private
  webpack-dev-server@2.11.5(webpack@3.12.0):
    webpack-dev-server: private
  webpack-merge@4.2.2:
    webpack-merge: private
  webpack-notifier@1.15.0:
    webpack-notifier: private
  webpack-sources@1.4.3:
    webpack-sources: private
  webpack@3.12.0:
    webpack: private
  websocket-driver@0.7.4:
    websocket-driver: private
  websocket-extensions@0.1.4:
    websocket-extensions: private
  whet.extend@0.9.9:
    whet.extend: private
  which-boxed-primitive@1.1.1:
    which-boxed-primitive: private
  which-builtin-type@1.2.1:
    which-builtin-type: private
  which-collection@1.0.2:
    which-collection: private
  which-module@2.0.1:
    which-module: private
  which-typed-array@1.1.19:
    which-typed-array: private
  which@1.3.1:
    which: private
  wide-align@1.1.5:
    wide-align: private
  window-size@0.1.0:
    window-size: private
  wordwrap@0.0.2:
    wordwrap: private
  worker-farm@1.7.0:
    worker-farm: private
  wrap-ansi@2.1.0:
    wrap-ansi: private
  wrap-fn@0.1.5:
    wrap-fn: private
  wrappy@1.0.2:
    wrappy: private
  ws@7.5.10:
    ws: private
  xmlhttprequest-ssl@1.6.3:
    xmlhttprequest-ssl: private
  xtend@4.0.2:
    xtend: private
  y18n@3.2.2:
    y18n: private
  yallist@2.1.2:
    yallist: private
  yargs-parser@7.0.0:
    yargs-parser: private
  yargs@8.0.2:
    yargs: private
  yauzl@2.10.0:
    yauzl: private
  yeast@0.1.2:
    yeast: private
ignoredBuilds:
  - core-js
  - gifsicle
  - optipng-bin
  - pngquant-bin
  - mozjpeg
  - node-sass
  - es5-ext
  - uglifyjs-webpack-plugin
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.1
pendingBuilds: []
prunedAt: Fri, 05 Sep 2025 06:19:40 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmmirror.com/
skipped:
  - bindings@1.5.0
  - file-uri-to-path@1.0.0
  - fsevents@1.2.13
  - fsevents@2.3.3
storeDir: E:\.pnpm-store\v10
virtualStoreDir: E:\a_c\vscode\cx\dome3\wechatAlliance\node_modules\.pnpm
virtualStoreDirMaxLength: 60
