{"name": "prettier", "version": "1.19.1", "description": "<PERSON><PERSON><PERSON> is an opinionated code formatter", "bin": "./bin-prettier.js", "repository": "prettier/prettier", "homepage": "https://prettier.io", "author": "<PERSON>", "license": "MIT", "main": "./index.js", "engines": {"node": ">=4"}, "scripts": {"prepublishOnly": "node -e \"assert.equal(require('.').version, require('..').version)\""}, "files": ["*.js"]}